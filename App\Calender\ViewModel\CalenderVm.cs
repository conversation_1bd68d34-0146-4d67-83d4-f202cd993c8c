﻿using Jobid.App.Calender.Models;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.ViewModel;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace Jobid.App.Calender.ViewModel
{
    public class CalenderVm
    {
        [Required(ErrorMessage = "Name cannot be null")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Start Date cannot be null")]
        public DateTime StartDate { get; set; }

        [Required(ErrorMessage = "End Time cannot be null")]
        public DateTime EndTime { get; set; }

        public string MeetingId { get; set; }

        public List<string> ExternalTeamMemberEmails { get; set; }

        public DateTime? EndDate { get; set; }

        [Required(ErrorMessage = "Location cannot be null")]
        public string Location { get; set; }

        public int? MeetLength { get; set; }

        public string MeetingDuration { get; set; }

        public bool MakeSchdulePrivate { get; set; } = false;

        public int NotifyMeInMinutes { get; set; }

        public NotifyMeVia NotifyMeVia { get; set; } = NotifyMeVia.Email;

        public CalenderScheduleType ScheduleType { get; set; }

        public string Frequency { get; set; }

        // [Required(ErrorMessage = "Invited Users cannot be null")]
        public List<string> InvitedUsers { get; set; }

        public MeetingStatus MeetingStatus { get; set; } = MeetingStatus.Active;

        public Guid UserId { get; set; }

        [JsonIgnore]
        public Guid CreatedBy { get; set; }

        [JsonIgnore]
        public string SubDomain { get; set; }

        [JsonIgnore]
        public string Host { get; set; }

        public Guid? AssignedMeetingOwner { get; set; }

        public MeetingOwnerTypes MeetingOwnerType { get; set; }

        public ReoccuringDeleteOptions? ReoccuringDeleteOptions { get; set; }

        public bool CreatedByAI { get; set; }

        public List<Base64VM>? AttachmentBase64 { get; set; }

        public CustomFrequencyDto? CustomFrequency { get; set; }

        [JsonIgnore]
        public string Token { get; set; }
    }
}
