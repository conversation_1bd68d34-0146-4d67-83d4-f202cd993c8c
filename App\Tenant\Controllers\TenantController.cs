﻿using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers;
using Jobid.App.Tenant.ViewModel;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Utils;
using Serilog;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.ViewModel.IdentityVM;

namespace Jobid.App.Tenant.Controllers
{
    [Route("api/[controller]")]
    [Produces("Application/json")]
    [ApiController]
    public class TenantController : Controller
    {
        private readonly IUnitofwork Services_Repo;

        private UserManager<User> UserManager;

        private SignInManager<User> SignInManager;
        private ILogger _logger = Log.ForContext<TenantController>();

        private ITenantSchema tenantSchema;

        public TenantController(IUnitofwork unitofwork, UserManager<User> _usermanager, SignInManager<User> signInManager, ITenantSchema tenantSchema)
        {
            this.Services_Repo = unitofwork;
            this.UserManager = _usermanager;
            this.SignInManager = signInManager;
            this.tenantSchema = tenantSchema;
        }

        #region Invite Users to Tenant
        /// <summary>
        /// Invite Users to Tenant
        /// </summary>
        /// <param name="emails"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
        [HttpPost]
        [Route("InviteUserToTenant")]
        public async Task<IActionResult> InviteUserToTenant(List<string> emails, string userId)
        {
            try
            {
                var subdomain = tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var result = await Services_Repo.TenantService.InviteUserToTenant(emails, userId, subdomain);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Invitation sent successfully.",
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Invitation failed.",
                        ResponseCode = "500",
                        Data = result
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                });
            }
            catch (InvalidOperationException ex)
            {
                Log.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Check if Tenant Exists
        /// <summary>
        /// Check if Tenant Exists
        /// </summary>
        /// <param name="subdomain"></param>
        /// <param name="emailDomain"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("CheckIfDomainExists")]
        public async Task<IActionResult> CheckIfTenantExists(string subdomain, string emailDomain)
        {
            var result = await Services_Repo.TenantService.CheckIfTenantExists(subdomain, emailDomain);
            return Ok(new ApiResponse<bool>
            {
                ResponseMessage = "Success",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Check if email is a valid custom email
        [HttpPost]
        [Route("CheckIfEmailIsValidCustomEmail")]
        public IActionResult CheckIfEmailIsValidCustomEmail([FromBody] ValidCustomEmailCheck model)
        {
            return ModelState.IsValid ? Ok(new ApiResponse<bool> { Data = true, ResponseCode = "200", ResponseMessage = "Valid custom email." }) : BadRequest(new ApiResponse<bool> { Data = false, ResponseCode = "400", ResponseMessage = "Invalid custom email." });
        }
        #endregion

        #region Initialize Tenant Registration
        /// <summary>
        /// Initialize Tenant Registration
        /// </summary>
        /// <param name="adminEmail"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("InitializeTenantRegistration/")]
        public async Task<IActionResult> InitializeTenantRegistration(string adminEmail)
        {
            try
            {
                var _user = await UserManager.FindByEmailAsync(adminEmail);
                if (_user != null)
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        Data = false,
                        ResponseCode = "409",
                        ResponseMessage = "Account with the supplied email already exists.",
                    });
                }
                var result = await this.Services_Repo.OPTService.RegisterTenantCreateToken(new NewOTPVM
                {
                    Identifier = adminEmail,
                    IdentifierType = OTPIdentifierType.Email,
                    TokenType = OTPTokenType.TenantRegistration,

                });
                if (result.ResponseCode == "200")
                {
                    return Ok(result);
                }
                else
                {
                    return BadRequest(result);
                }
            }
            catch (RecordAlreadyExistException ex)
            {
                Log.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "409",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Register Tenant
        /// <summary>
        /// Register Tenant
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("RegisterTenant")]
        public async Task<IActionResult> RegisterTenant([FromForm] RegisterTenantVM model)
        {
            var tenant = await Services_Repo.TenantService.RegisterTenant(model);
            if (tenant.ResponseCode == "200")
            {
                //var subscription = await Services_Repo.SubscriptionServices.CreateSubscription(tenant.Data as JobPaysSubscribeDto);
                //if (subscription.ResponseCode != "200")
                //{
                //    tenant.ResponseMessage = "Comapny sign-up was successful but free plan activated failed. Kindly contact admin or support";
                //    tenant.ResponseCode = subscription.ResponseCode;
                //    tenant.Data = false;

                //    return BadRequest(tenant);
                //}

                return Ok(tenant);
            }
            else
            {
                return BadRequest(tenant);
            }
        }
        #endregion

        #region Register company/tenant for an existing user
        /// <summary>
        /// Create Company For Existing User
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("CreateCompanyForExistingUser")]
        public async Task<IActionResult> CreateCompanyForExistingUser([FromForm] CreateTenantForExistingUserVM model)
        {
            var tenant = await Services_Repo.TenantService.CreateCompanyForExistingUser(model);
            if (tenant.ResponseCode == "200")
            {
                return Ok(tenant);
            }
            else
            {
                return BadRequest(tenant);
            }
        }
        #endregion

        #region Update Tenant Details
        /// <summary>
        /// Uodate Tenant Details
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UpdateTenantDetails")]
        public async Task<IActionResult> UpdateTenantDetails([FromForm] UpdateTenantDto model)
        {
            var result = await Services_Repo.TenantService.UpdateTenantDetails(model);
            if (result.ResponseCode == "200")
                return Ok(result);
            else
                return BadRequest(result);
        }
        #endregion

        #region Run Migrations on a Tenant
        /// <summary>
        /// Run Migrations on a Tenant
        /// </summary>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RunMigrations/{subdomain}")]
        public async Task<IActionResult> RunMigrations(string subdomain)
        {
            try
            {
                var result = await new TenantSchema().RunMigrations(subdomain);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseMessage = "Migration was" + (" successful"),
                        ResponseCode = "200",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseMessage = "Migration" + (" failed"),
                        ResponseCode = "500",
                        Data = result
                    });
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex.Message);
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    ResponseMessage = ex.ToString(),
                });
            }
        }
        #endregion

        #region Get Company User Invites
        [HttpGet]
        [Authorize]
        [Route("GetCompanyUserInvites/{tenantId}")]
        public async Task<IActionResult> GetUserInvites([FromRoute] string tenantId, [FromQuery] PaginationParameters parameters, CompanyUserInviteStatus companyUserInviteStatus)
        {
            try
            {
                var invites = await Services_Repo.CompanyUserInviteService.GetInvites(tenantId, parameters, companyUserInviteStatus);
                if (invites == null)
                {
                    return BadRequest(new ApiResponse<List<CompanyUserInviteVM>>
                    {
                        ResponseCode = "500",
                        ResponseMessage = "No invites fetched. Please try again.",
                        Data = null,
                    });
                }
                else
                {
                    return Ok(new ApiResponse<List<CompanyUserInviteVM>>
                    {
                        ResponseCode = "00",
                        ResponseMessage = "Successful.",
                        Data = invites,
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<Page<CompanyUserInviteVM>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong with sending the invite. Please try again",
                    Data = null,
                });
            }
        }
        #endregion

        #region Give user app permission
        /// <summary>
        /// This endpoints gives permission to a specific app to a user
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="app"></param>
        /// <param name="tenantId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddUserToAppPermission")]
        public async Task<IActionResult> AddUserToAppPermission(string userId, Applications app, string tenantId)
        {
            var response = await Services_Repo.TenantService.AddUserToAppPermission(userId, app.ToString(), tenantId);
            return response ? Ok(new ApiResponse<bool>
            {
                ResponseCode = "200",
                ResponseMessage = "Success",
                Data = true
            })
            : BadRequest(new ApiResponse<bool>
            {
                ResponseCode = "500",
                ResponseMessage = "Failed",
                Data = false
            });
        }
        #endregion

        #region Get user permitted apps
        /// <summary>
        /// Get user permitted packages/applications
        /// This endpoint is mainly used by mobile apps to get the packages/applications a user has access to
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUserPermittedApps/{userId}")]
        public async Task<IActionResult> GetUserPermittedApps([FromRoute] string userId)
        {
            var subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var response = await Services_Repo.TenantService.GetUserPermittedApps(userId, subdomain);
            return Ok(response);
        }
        #endregion

        #region Delete Company User Invite
        [HttpDelete]
        [Route("DeleteTenant/{subdomain}")]
        public async Task<IActionResult> GetUserInvites([FromRoute] string subdomain, [FromQuery] string deletedBy = null, [FromQuery] string deletionReason = null)
        {
            var subdomainFromHeader = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
            var response = await Services_Repo.TenantService.DeleteTenant(subdomain, subdomainFromHeader, deletedBy, deletionReason);
            return response.ResponseCode == "200" ? Ok(response)
                : BadRequest(response);
        }
        #endregion

        #region Get Tenant
        [HttpGet]
        [Route("GetTenant/{subdomain}")]
        public async Task<IActionResult> GetTenant([FromRoute] string subdomain)
        {
            try
            {
                var response = await Services_Repo.TenantService.GetTenantBySubdomain(subdomain);
                if (response == null)
                {
                    return BadRequest(response);
                }
                else
                {
                    return Ok(response);
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong while fetching the company. Please try again.",
                    Data = false,
                });
            }
        }
        #endregion

        #region Get all tenants/companies
        /// <summary>
        ///  Gets all tenants.companies
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllTenants")]
        public async Task<IActionResult> GetAllTenants()
        {
            var response = await Services_Repo.TenantService.GetAllTenants();
            return Ok(response);
        }
        #endregion

        #region Get all tenants/companies - AI
        /// <summary>
        ///  Gets all tenants.companies
        /// </summary>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpGet]
        [Route("AI/GetAllTenant")]
        public async Task<IActionResult> AIGetAllTenants()
        {
            var response = await Services_Repo.TenantService.GetAllTenants();
            return Ok(response);
        }
        #endregion

        #region Get all deleted/trashed tenants
        /// <summary>
        /// Gets all deleted/trashed tenants
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDeletedTenants")]
        public async Task<IActionResult> GetDeletedTenants()
        {
            var response = await Services_Repo.TenantService.GetDeletedTenants();
            return Ok(response);
        }
        #endregion

        #region Get Tenant Users
        [HttpGet]
        [Authorize]
        [Route("GetTenantUsers/")]
        public async Task<IActionResult> GetTenantUsers([FromQuery] string tenantId)
        {
            try
            {
                var subdomain = this.tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var response = await Services_Repo.TenantService.GetTenantUsers(tenantId ?? subdomain);
                return Ok(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return BadRequest(new ApiResponse<List<UserCompaniesVM>>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong while fetching the company users. Please try again.",
                    Data = null,
                });
            }
        }
        #endregion

        #region Get Top performing companies
        [HttpGet]
        [Route("GetTopPerformingCountries/")]
        public async Task<IActionResult> GetTopPerformingCountries()
        {
            try
            {
                var response = await Services_Repo.TenantService.TopPerformingCountries();
                return Ok(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return BadRequest(new ApiResponse<dynamic>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong while fetching the top performing countries. Please try again.",
                    Data = null,
                });
            }
        }
        #endregion

        #region Get Top performing companies
        [HttpGet]
        [Route("CountriesPercentage/")]
        public async Task<IActionResult> GetAllCountriesPercentage()
        {
            try
            {
                var response = await Services_Repo.TenantService.GetAllCountriesPercentage();
                return Ok(response);
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return BadRequest(new ApiResponse<dynamic>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong while fetching the top performing countries. Please try again.",
                    Data = null,
                });
            }
        }
        #endregion

        #region Test Schema Creation And Migration
        /// <summary>
        /// Test Schema Creation And Migration
        /// </summary>
        /// <param name="schema"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("TestSchemaCreationAndMigration")]
        public async Task<IActionResult> TestSchemaCreationAndMigration(string schema)
        {
            try
            {
                var schemaCreated = await this.tenantSchema.NewSchema(schema);
                if (schemaCreated)
                {
                    var response = await this.tenantSchema.RunMigrations(schema);
                    if (response)
                        return Ok(new ApiResponse<bool>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Schema creation and migration was successful.",
                            Data = response,
                        });
                    else
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "500",
                            ResponseMessage = "Schema creation and migration failed. Please try again.",
                            Data = response,
                        });
                }
                else
                {
                    return BadRequest(new ApiResponse<dynamic>
                    {
                        ResponseCode = "500",
                        ResponseMessage = "Schema creation failed. Please try again.",
                        Data = null,
                    });
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                return BadRequest(new ApiResponse<dynamic>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong while testing schema creation and migration. Please try again.",
                    Data = null,
                });
            }
        }
        #endregion
    }
}
