﻿using Jobid.App.ActivityLog.Model;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Models;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Filters;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProjectManagement.Controllers;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using StackExchange.Redis;
using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace Jobid.App.AdminConsole.Controllers
{
    [CustomAuthorize]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Onboarding)]
    [Produces("Application/json")]
    [ApiController]
    public class AdminController : BaseController
    {
        private readonly IUnitofwork Services_Repo;
        private ILogger _logger = Log.ForContext<AdminController>();

        public AdminController(IUnitofwork unitofwork)
        {
            this.Services_Repo = unitofwork;
        }

        #region Get activity logs
        /// <summary>
        /// Get activity logs
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <param name="sDate"></param>
        /// <param name="eDate"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetActivityLogs")]
        public async Task<IActionResult> GetActivityLogs([Required] DateTime sDate, [Required] DateTime eDate, int pageNumber = 1, int pageSize = 20)
        {
            var result = await Services_Repo.AdminService.GetActivityLogs(pageNumber, pageSize, sDate, eDate);
            return Ok(new ApiResponse<Page<Activity>>
            {
                Data = result,
                ResponseCode = "200",
                ResponseMessage = "Success",
            });
        }
        #endregion

        #region Get activity logs with filters
        /// <summary>
        /// This endpoint gets the activity logs with filters for client admin
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetActivityLogsWithFilters")]
        public async Task<IActionResult> GetActivityLogsByFilter(AcitivityLogFiltersDto model)
        {
            var result = await Services_Repo.AdminService.GetActivitiesWithFiltersForClientAdmin(model);
            return Ok(result);
        }
        #endregion

        #region Get company logo
        /// <summary>
        /// Gets company logo
        /// </summary>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetCompanyLogo")]
        public async Task<IActionResult> GetCompanyLogo([FromQuery] string subdomain)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                if (subdomain == null) { BadRequest("Id is null"); }
                var result = await Services_Repo.AdminService.GetAdminCompanyLogo(subdomain);
                if (result == null)
                {
                    return Ok(new ApiResponse<string>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "logo is empty",
                    });
                }
                return Ok(new ApiResponse<string>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success getting logo",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update admin company profile
        /// <summary>
        /// Updates admin comapny profile
        /// </summary>
        /// <param name="profile"></param>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateAdminCompanyProfile")]
        public async Task<IActionResult> UpdateAdminCompanyProfile([FromBody] UpdateAdminProfileDto profile, [Required] string subdomain)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                if (string.IsNullOrEmpty(subdomain)) { BadRequest("subdomain is required"); }
                var result = await Services_Repo.AdminService.UpdateAdminCompanyProfile(subdomain, profile);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Updated successfully",
                    });
                }
                return BadRequest(new ApiResponse<bool>
                {
                    Data = result,
                    ResponseCode = "400",
                    ResponseMessage = "Failed to update",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Update admin company logo
        /// <summary>
        /// Updates admin company logo
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateAdminCompanyLogo")]
        public async Task<IActionResult> UpdateAdminCompanyLogo([FromForm] UpdateCompanyLogoDto model)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var subdomain = HttpContext.Request.Headers["subdomain"];
                var result = await Services_Repo.AdminService.UpdateAdminCompanyLogo(subdomain, model);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "logo successfully updated",
                    });
                }
                return BadRequest(new ApiResponse<bool>
                {
                    Data = result,
                    ResponseCode = "400",
                    ResponseMessage = "Failed to upload logo",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Get all packages
        /// <summary>
        /// Get all packages
        /// </summary>
        /// <param name="subdomain"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllPackages")]
        public async Task<IActionResult> GetAllPackages([FromQuery] string subdomain)
        {
            try
            {
                if (subdomain == null) { BadRequest("Id is null"); }
                var result = await Services_Repo.AdminService.GetAllPackages(subdomain);
                return Ok(new ApiResponse<IEnumerable>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success getting packages",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Get total active employees
        /// <summary>
        /// Gets the total active employees
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("TotalActiveEmployeesCount")]
        public async Task<IActionResult> GetTotalActiveEmployees()
        {
            try
            {
                var result = await Services_Repo.AdminService.GetActiveEmployeeCount();
                return Ok(new ApiResponse<long>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<long>
                {
                    Data = 0,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Total suspended user count
        /// <summary>
        /// Gets suspended user count
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("TotalSuspendedUsersCount")]
        public async Task<IActionResult> GetTotalSuspendedUsersCount()
        {
            try
            {
                var result = await Services_Repo.AdminService.GetSuspendedUsersCount();
                return Ok(new ApiResponse<int>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<int>
                {
                    Data = 0,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Give user full access
        /// <summary>
        /// This gives a user full access to an AI agent or to an applications like Joble, Jobpays
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GiveUserFullAccess")]
        public async Task<IActionResult> GiveUserFullAccess(GiveUserFullAccessDto model)
        {
            if (model.Agent is not null)
                model.App = Applications.All.ToString();

            var response = await Services_Repo.AdminService.GiveUserFullAccess(model);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Downgrade user to basic  access
        /// <summary>
        /// This gives a user full access to an AI agent or to an applications like Joble, Jobpays
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DowngradeUserToBasicAccess")]
        public async Task<IActionResult> DowngradeUserToBasicAccess(DowngradeUserToBasicAccessDto model)
        {
            if (model.Agent is not null)
                model.App = Applications.All.ToString();

            var response = await Services_Repo.AdminService.DowngradeUserToBasicAccess(model);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Revoke user access
        /// <summary>
        /// This revokes a user access to an AI agent or to an applications like Joble, Jobpays
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RevokeUserAccess")]
        public async Task<IActionResult> RevokeUserAccess(RevokeUserAccessDto model)
        {
            if (model.Agent is not null)
                model.App = Applications.All.ToString();

            var response = await Services_Repo.AdminService.RevokeUserAccess(model);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Remove user from company
        /// <summary>
        ///  Removes a user from company
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpDelete]
        [Route("RemoveUserFromCompany/{userId}")]
        public async Task<IActionResult> RemoveUserFromCompany([FromRoute] string userId)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
            var response = await Services_Repo.AdminService.RemoveUserFromCompany(userId, subdomain);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Unsuspend User
        /// <summary>
        /// Unsuspend employee
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPut]
        [Route("UnsuspendEmployee/{userId}")]
        public async Task<IActionResult> UnsuspendUser(string userId)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await Services_Repo.AdminService.UnSuspendEmployee(userId);
                if (result)
                {
                    return Ok(new ApiResponse<bool>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "User successfully unsuspended",
                    });
                }
                return BadRequest(new ApiResponse<bool>
                {
                    Data = result,
                    ResponseCode = "400",
                    ResponseMessage = "Failed to unsuspend user",
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                });
            }
        }
        #endregion

        #region Get total suspended users
        /// <summary>
        /// This gets the toatl suspended users
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("TotalSuspendedUsers")]
        public async Task<IActionResult> GetTotalSuspendedUsers([FromQuery] PaginationParameters parameters)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await Services_Repo.AdminService.GetSuspendedUsers(parameters, subdomain);
                if (result == null)
                {
                    return StatusCode(404, new ApiResponse<Page<DisplayUserDto>>
                    {
                        ResponseMessage = "No suspended user record found",
                        ResponseCode = "404",
                        Data = result
                    });
                }
                return Ok(new ApiResponse<Page<DisplayUserDto>>
                {
                    ResponseMessage = "Success getting all suspended users",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Get employee distribution
        /// <summary>
        /// Gets employee distribution
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("EmployeeDistribution")]
        public async Task<IActionResult> GetActiveEmployeesByPackages()
        {
            try
            {
                var result = await Services_Repo.AdminService.GetActiveEmployeesByPackages();
                if (result == null)
                {
                    return Ok(new ApiResponse<Dictionary<string, int>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "No record found",
                    });
                }
                else
                {
                    return Ok(new ApiResponse<Dictionary<string, int>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Success",
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<Dictionary<string, int>>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Employee distribution by frequency
        /// <summary>
        /// Employee by frequency
        /// </summary>
        /// <param name="frequency"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("EmployeeDistributionByFrequency")]
        public async Task<IActionResult> EmployeeDistributionByFrequency(EmployeeDistributionFrequency frequency)
        {
            try
            {
                var result = await Services_Repo.AdminService.EmployeeDistributionByFrequency(frequency);
                if (result == null)
                {
                    return Ok(new ApiResponse<Dictionary<string, int>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "No record found",
                    });
                }
                else
                {
                    return Ok(new ApiResponse<Dictionary<string, int>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Success",
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<Dictionary<string, int>>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region User distribution by location
        /// <summary>
        /// Get user distribution grouped by location
        /// </summary>
        /// <param name="frequency">Time period filter</param>
        /// <param name="application">Application (defaults to Joble)</param>
        /// <returns>User distribution by country</returns>
        [HttpGet]
        [Route("UserDistributionByLocation")]
        public async Task<IActionResult> UserDistributionByLocation(
            EmployeeDistributionFrequency frequency,
            Applications application = Applications.Joble)
        {
            var result = await Services_Repo.AdminService.GetUserDistributionByLocation(frequency, application);
            return Ok(result);
        }
        #endregion

        #region Get most used packages by months
        /// <summary>
        /// Gets most used packages by months
        /// </summary>
        /// <param name="frequency"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetMostUsedPackageByMonths")]
        public async Task<IActionResult> GetPackageUsageByMonths(MostUsedPackagesFrequency frequency)
        {
            try
            {
                var result = await Services_Repo.AdminService.GetPackageUsageByMonths(frequency);
                if (result == null)
                {
                    return Ok(new ApiResponse<List<PackageMonthlyUsage>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "No record found",
                    });
                }
                else
                {
                    return Ok(new ApiResponse<List<PackageMonthlyUsage>>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Success",
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<Dictionary<string, int>>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Package dashboard data
        /// <summary>
        /// Package dashbaord data
        /// </summary>
        /// <param name="application"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("PackagesDashboard")]
        public async Task<IActionResult> GetPackagesDashboard(Applications application)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await Services_Repo.AdminService.GetPackagesDashboard(application, subdomain);
                return Ok(new ApiResponse<PackageDashBoard>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<PackageDashBoard>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Total pending invite Count
        /// <summary>
        /// Gets total pending invite Count
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("TotalPendingInviteCount")]
        public async Task<IActionResult> GetTotalPendingInviteCount(string subdomain = null, Applications? app = null)
        {
            try
            {
                var result = await Services_Repo.AdminService.GetPendingInvitationsCount(subdomain, app);
                return Ok(new ApiResponse<int>
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Success",
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<int>
                {
                    Data = 0,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Total pending invites
        /// <summary>
        /// This endpoint gets the total pending invites details
        /// </summary>
        /// <param name="pageSize"></param>
        /// <param name="pageNumber"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("TotalPendingInvites")]
        public async Task<IActionResult> GetTotalPendingInvite(int pageNumber = 1, int pageSize = 10, Applications? app = null)
        {
            var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
            var result = await Services_Repo.AdminService.GetPendingInvitations(subdomain, app, pageNumber, pageSize);
            return Ok(await ConvertDateTimeToLocalDateTime(result));
        }
        #endregion

        #region Get used and remaining liecence count
        /// <summary>
        /// Get used and remaining liecense count
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="app"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUsedAndRemainingLicenceCount")]
        public async Task<IActionResult> GetUsedAndRemainingLicenceCount([Required] string tenantId, Applications app)
        {
            try
            {
                var result = await Services_Repo.AdminService.GetUsedAndRemainingLicenceCount(tenantId, app);
                return Ok(result);

            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Data = null,
                    ResponseCode = "404",
                    ResponseMessage = ex.Message
                });
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(new ApiResponse<string>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Get all recent activities
        /// <summary>
        /// Gets all recent activities
        /// </summary>
        /// <param name="pageNumber"></param>
        /// <param name="pageSize"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllRecentActivities")]
        public async Task<IActionResult> GetAllRecentActivities(int pageNumber = 1, int pageSize = 1)
        {
            try
            {
                var result = await Services_Repo.AdminService.GetAllRecentActivities(pageNumber, pageSize);
                if (result.Item1 != null)
                {
                    return Ok(new ApiResponse<(IEnumerable<RecentActivitiesDto>, long)>
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Success",
                    });
                }
                else
                {
                    return NotFound(new ApiResponse<RecentActivitiesDto>
                    {
                        Data = null,
                        ResponseCode = "404",
                        ResponseMessage = "No activity found"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<RecentActivitiesDto>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Gets all users - search feature
        /// <summary>
        /// Gets all users
        /// </summary>
        /// <param name="userParaDtoVm"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAndSearchAllUsers")]
        public async Task<ApiResponse<Page<DisplayUserDto>>> GetAllUsers([FromQuery] UserParaDtoVm userParaDtoVm)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await this.Services_Repo.AdminService.GetAndSearchAllUsers(subdomain, userParaDtoVm.PageNumber,
                    userParaDtoVm.PageSize, userParaDtoVm.SearchWord, userParaDtoVm.App, userParaDtoVm.status);
                if (result == null)
                {
                    return new ApiResponse<Page<DisplayUserDto>>
                    {
                        ResponseMessage = "No user record found",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                return new ApiResponse<Page<DisplayUserDto>>
                {
                    ResponseMessage = "Success getting all users",
                    ResponseCode = "200",
                    Data = ConvertDateTimeToLocalDateTime(result).Result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<Page<DisplayUserDto>>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Gets all employees by package
        /// <summary>
        /// Gets all the employees by distribution
        /// </summary>
        /// <param name="app"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAllEmployeesByPackage")]
        public async Task<IActionResult> GetAllEmployeesByPackage(Applications app)
        {
            try
            {
                var result = await this.Services_Repo.AdminService.GetEmployeesByPackage(app);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Assign user to a role
        /// <summary>
        /// Assign role to a user
        /// </summary>
        /// <param name="roleId"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AssignUserToRole")]
        public async Task<IActionResult> AssignUserToRole(string roleId, string userId)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var response = await Services_Repo.AdminService.AssignUserToRole(userId, roleId);
                if (response.ResponseCode == "200")
                {
                    await this.Services_Repo.AdminService.LogUserOut(userId);
                    return Ok(response);
                }
                else
                    return BadRequest(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE, nameof(AssignUserToRole), $"RoleId: {roleId} - UserId: {userId}");
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "500",
                    ResponseMessage = "Something went wrong, please try again later",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    Data = false
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString(), nameof(AssignUserToRole), $"RoleId: {roleId} - UserId: {userId}");
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseMessage = "Something went wrong, please try again later",
                    DevResponseMessage = ex.ToString(),
                    ResponseCode = "500",
                    Data = false
                });
            }
        }
        #endregion

        #region Get deativated users
        /// <summary>
        /// Get deativated users
        /// </summary>
        /// <param name="userParaDtoVm"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAndSearchDeactivatedUsers")]
        public async Task<ApiResponse<Page<DisplayUserDto>>> GetDeactivatedUsers([FromQuery] UserParaDtoVm userParaDtoVm)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await this.Services_Repo.AdminService.GetAllDeactivatedEmployees(userParaDtoVm.PageNumber, userParaDtoVm.PageSize, subdomain, userParaDtoVm.SearchWord);
                if (result == null)
                {
                    return new ApiResponse<Page<DisplayUserDto>>
                    {
                        ResponseMessage = "No deactivated user found",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                return new ApiResponse<Page<DisplayUserDto>>
                {
                    ResponseMessage = "Success getting deactivated users",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<Page<DisplayUserDto>>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                };
            }
        }
        #endregion

        #region Get user by id
        [HttpGet]
        [Route("GetUserById")]
        public async Task<IActionResult> GetUserById([FromQuery] string userId)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"];
                var result = await this.Services_Repo.AdminService.ViewUserById(userId, subdomain);
                return Ok(new GenericResponse
                {
                    ResponseMessage = "Success",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE + ",\n " + ex.Message,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get packages with user permissions
        [HttpGet]
        [Route("PackagesWithUserPermissions")]
        public async Task<IActionResult> GetPackagesWithUserPermissions([FromQuery] string userId)
        {
            try
            {

                var result = await this.Services_Repo.AdminService.GetPackagesWithUserPermissions(userId);
                return Ok(new GenericResponse
                {
                    ResponseMessage = "Success",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get employee roles and permission
        [HttpGet]
        [Route("EmployeeRolesAndPermission")]
        public async Task<IActionResult> GetEmployeeRolesAndPermission(string roleId)
        {
            var result = await this.Services_Repo.AdminService.GetEmployeeRolesAndPermission(roleId);
            return Ok(new GenericResponse
            {
                ResponseMessage = "Success",
                ResponseCode = "200",
                Data = result
            });
        }
        #endregion

        #region Get All Roles For A Package
        /// <summary>
        /// Get All Roles For A Package
        /// </summary>
        /// <param name="appName"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeRolesByPackge")]
        public async Task<IActionResult> GetEmployeeRoles(Applications appName)
        {
            try
            {
                var result = await this.Services_Repo.AdminService.GetEmployeeRoles(appName);
                return Ok(new GenericResponse
                {
                    ResponseMessage = "Success",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later",
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get employee role
        /// <summary>
        /// Gets user role
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeRole")]
        public async Task<IActionResult> GetEmployeeRole(string userId)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await this.Services_Repo.AdminService.GetUserRole(userId, subdomain);
                return Ok(new GenericResponse
                {
                    ResponseMessage = "Success",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Get Role By Id
        /// <summary>
        /// Gets role by id
        /// </summary>
        /// <param name="roleId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRoleUsers/{roleId}")]
        public async Task<IActionResult> GetRoleUsers(string roleId)
        {
            try
            {
                var result = await this.Services_Repo.AdminService.GetRoleUsers(roleId);
                return Ok(new GenericResponse
                {
                    ResponseMessage = "Success",
                    ResponseCode = "200",
                    Data = result
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                });
            }
        }
        #endregion

        #region Suspend employee
        /// <summary>
        /// Suspend employee
        /// </summary>
        /// <param name="suspendUserDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SuspendEmployee")]
        public async Task<IActionResult> SuspendEmployee(SuspendUser suspendUserDto)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                if (suspendUserDto.UserId == CurrentUserId.Value.ToString())
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = null,
                        ResponseCode = "400",
                        ResponseMessage = "You cannot suspend yourself"
                    });
                }

                var sudomain = HttpContext.Request.Headers["subdomain"].ToString();
                var role = await this.Services_Repo.AdminService.GetUserRole(suspendUserDto.UserId, sudomain);
                if (role == "SuperAdmin")
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = null,
                        ResponseCode = "400",
                        ResponseMessage = "You cannot suspend a super admin"
                    });
                }

                var result = await this.Services_Repo.AdminService.SuspendEmployee(suspendUserDto);
                if (result)
                {
                    // Get token from headers and blacklist it
                    var token = HttpContext.Request.Headers["Authorization"].ToString();
                    await this.Services_Repo.Userservice.BlacklistJwtToken(token);

                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Employe suspended successfully",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to suspend employee"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Remove User Application Permission
        /// <summary>
        /// Remove User Application Permission
        /// </summary>
        /// <param name="package"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("RemoveUserAppPermission")]
        public async Task<IActionResult> RemoveUserAppPermission([FromBody] Applications package, string userId)
        {
            try
            {
                var result = await this.Services_Repo.AdminService.RemoveUserAppPermission(userId, package);
                if (result)
                {
                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "App permission successfully removed",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to remove permission"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later"
                });
            }
        }
        #endregion

        #region Update Role
        /// <summary>
        /// Update Employee Role
        /// </summary>
        /// <param name="permissionDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateEmployeeRole")]
        public async Task<IActionResult> UpdatePermission([FromBody] UpdateRoleDto permissionDto)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await this.Services_Repo.AdminService.UpdateRole(permissionDto);
                if (result)
                {
                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Role permission updated successfully",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to update permission"
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Failed to update permission, please try again later"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Failed to update permission, please try again later"
                });
            }
        }
        #endregion

        #region Create Roles
        [HttpPost]
        [Route("CreateRoles")]
        public async Task<IActionResult> CreateRoles(string roleName, string appName)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await this.Services_Repo.AdminService.CreateRoles(roleName, appName);
                if (result)
                {
                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Role successfully created",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to create role"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Create Permission
        [HttpPost]
        [Route("CreatePermission")]
        public async Task<IActionResult> CreatePermissions([FromBody] List<CreatePermission> permission)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await this.Services_Repo.AdminService.CreatePermissions(permission);
                if (result)
                {
                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Permission successfully created",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to create permission"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Delete Roles
        /// <summary>
        /// Delete Roles
        /// </summary>
        /// <param name="roleIds"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteRoles")]
        public async Task<IActionResult> DeleteRole(List<string> roleIds)
        {
            try
            {
                await this.Services_Repo.AdminService.DeleteRole(roleIds);

                return Ok(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "200",
                    ResponseMessage = "Role has been successfully removed",
                });

            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later"
                });
            }
            catch (OperationFailedException ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later"
                });
            }
        }
        #endregion

        #region Assign Permissions To Role
        /// <summary>
        /// Assign Permissions To Role
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AssignPermissionsToRole")]
        public async Task<IActionResult> AssignPermissionsToRole(AssignPermissionsToRoleDto model)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await this.Services_Repo.AdminService
                    .AssignPermissionsToRole(model.RoleId, model.PermissionIds);

                if (result.ResponseCode == "200")
                    return Ok(result);
                else
                    return BadRequest(result);
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "404",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later"
                });
            }
            catch (Exception ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "500",
                    DevResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseMessage = "Something went wrong, please try again later"
                });
            }
        }
        #endregion

        #region Deletes An Employee - Soft
        /// <summary>
        /// Deletes An Employee
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteUser")]
        public async Task<IActionResult> DeleteUser(string userId)
        {
            var result = await this.Services_Repo.AdminService.DeleteUser(userId);
            if (result)
            {
                return Ok(new GenericResponse
                {
                    Data = result,
                    ResponseCode = "200",
                    ResponseMessage = "Successfully deleted employee",
                });
            }
            else
            {
                return BadRequest(new GenericResponse
                {
                    Data = result,
                    ResponseCode = "400",
                    ResponseMessage = "Failed to delete employee"
                });
            }
        }
        #endregion

        #region Change Employee Role
        [HttpPost]
        [Route("ChangeEmployeeRoleById")]
        public async Task<IActionResult> ChangeEmployeeRole([FromBody] UpdateEmployeeRole role)
        {
            try
            {
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var result = await this.Services_Repo.AdminService.ChangeRole(role.RoleName, role.UserId, role.appName);
                if (result)
                {
                    await this.Services_Repo.AdminService.LogUserOut(role.UserId);
                    return Ok(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "200",
                        ResponseMessage = "Role successfully changed",
                    });
                }
                else
                {
                    return BadRequest(new GenericResponse
                    {
                        Data = result,
                        ResponseCode = "400",
                        ResponseMessage = "Failed to change role"
                    });
                }
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        #region Get Product Updates
        [HttpGet("productupdates/sent")]
        public async Task<ApiResponse<Page<SentProductUpdateDto>>> GetSentProductUpdates([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await Services_Repo.ProductUpdateService.GetSentProductUpdates(subdomain, page, pageSize);

                if (result.Items.Length == 0)
                {
                    return new ApiResponse<Page<SentProductUpdateDto>>
                    {
                        ResponseMessage = "No sent product update found",
                        ResponseCode = "200",
                        Data = result
                    };
                }
                return new ApiResponse<Page<SentProductUpdateDto>>
                {
                    ResponseMessage = "Success getting sent product updates",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<Page<SentProductUpdateDto>>
                {
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE,
                    ResponseCode = "500",
                    Data = null
                };
            }
        }

        [HttpGet("productupdates/deleted")]
        public async Task<ApiResponse<Page<DeletedProductUpdateDto>>> GetDeletedProductUpdates([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"].ToString();
                var result = await Services_Repo.ProductUpdateService.GetDeletedProductUpdates(subdomain, page, pageSize);

                if (result.Items.Length == 0)
                {
                    return new ApiResponse<Page<DeletedProductUpdateDto>>
                    {
                        ResponseMessage = "No deleted product update found",
                        ResponseCode = "200",
                        Data = result
                    };
                }

                return new ApiResponse<Page<DeletedProductUpdateDto>>
                {
                    ResponseMessage = "Success getting deleted product updates",
                    ResponseCode = "200",
                    Data = result
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<Page<DeletedProductUpdateDto>>
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                };
            }
        }

        #endregion

        #region Edit employee
        /// <summary>
        /// Edit employee details
        /// </summary>
        /// <param name="updateDto"></param>
        /// <returns></returns>
        [HttpPost]
        [AllowAnonymous]
        [Route("EditEmployee")]
        public async Task<IActionResult> UpdateEmployee(EmployeeUpdate updateDto)
        {
            try
            {
                var subdomain = HttpContext.Request.Headers["subdomain"];
                GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
                var response = await this.Services_Repo.AdminService.UpdateEmployee(updateDto);
                if (response.ResponseCode == "200")
                    return Ok(response);
                return BadRequest(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new GenericResponse
                {
                    Data = null,
                    ResponseCode = "500",
                    ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE
                });
            }
        }
        #endregion

        // Company Deletion Request Section //

        #region Company Deletion Request
        /// <summary>
        /// Company Deletion Request
        /// </summary>
        /// <param name="companyDeletionRequestDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CompanyDeletionRequest")]
        public async Task<IActionResult> CompanyDeletionRequest([FromBody] RequestForCompanyDeletionDto companyDeletionRequestDto)
        {
            companyDeletionRequestDto.RequestedBy = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.RequestForCompanyDeletion(companyDeletionRequestDto);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Cancel Company Deletion Request
        /// <summary>
        /// Cancel Company Deletion Request
        /// </summary>
        /// <param name="requestId"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CancelCompanyDeletionRequest")]
        public async Task<IActionResult> CancelCompanyDeletionRequest(string requestId)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.CancelCompanyDeletion(requestId);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Get Company Deletion Requests
        [HttpGet]
        [Route("GetCompanyDeletionRequests")]
        public async Task<IActionResult> GetCompanyDeletionRequests(string tenentId)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.GetCompanyDeletionRequests(tenentId);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        // Localization Section //

        #region Add Or Update Localizations
        /// <summary>
        /// Add or Update Localizations
        /// </summary>
        /// <param name="localizationDtos"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddOrUpdateLocalizations")]
        public async Task<IActionResult> AddOrUpdateLocalizations([FromBody] AddOrUpdateLocalizationDto localizationDtos)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.AddOrUpdateLocalization(localizationDtos);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Get Localizations
        /// <summary>
        /// Get Localization Details
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetLocalizationDetails")]
        public async Task<IActionResult> GetLocalizations([FromQuery] string userId = null)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.GetLocalization(userId);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        // Password Policy Section //
        #region Add Or Update Password Policy
        /// <summary>
        /// Add Or Update Password Policy
        /// </summary>
        /// <param name="passwordPolicyDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddOrUpdatePasswordPolicy")]
        public async Task<IActionResult> AddOrUpdatePasswordPolicy([FromBody] PasswordPolicyDto passwordPolicyDto)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.AddOrUpdatePasswordPolicy(passwordPolicyDto);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Get Password Policy
        /// <summary>
        /// Get Password Policy
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPasswordPolicy")]
        public async Task<IActionResult> GetPasswordPolicy()
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.GetPasswordPolicy();
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        // Two Factor Authentication Policy Section //

        #region Add Or Update 2FA Policy
        /// <summary>
        /// Add Or Update 2FA Policy
        /// </summary>
        /// <param name="twoFactorAuthenticationPolicyDto"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddOrUpdate2FAPolicy")]
        public async Task<IActionResult> AddOrUpdate2FAPolicy([FromBody] TwoFactorSettingsDto twoFactorAuthenticationPolicyDto)
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.AddOrUpdateTwoFactorSettings(twoFactorAuthenticationPolicyDto);
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Get 2FA Policy
        /// <summary>
        /// Get 2FA Policy
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("Get2FAPolicy")]
        public async Task<IActionResult> Get2FAPolicy()
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.GetTwoFactorSettings();
            return response.ResponseCode == "200" ? Ok(response) : BadRequest(response);
        }
        #endregion

        #region Log All Users Out
        /// <summary>
        ///  Log All Users Out
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        [Route("LogAllUsersOut")]
        public async Task<IActionResult> LogAllUsersOut()
        {
            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var result = await this.Services_Repo.AdminService.LogAllUsersOut();
            return result.ResponseCode == "200" ? Ok(result) : BadRequest(result);
        }
        #endregion

        #region Billing Information Management

        /// <summary>
        /// Add billing information for the company
        /// </summary>
        /// <param name="model">Billing information data</param>
        /// <returns></returns>
        [HttpPost]
        [Route("add-billing-information")]
        public async Task<IActionResult> AddBillingInformation([FromBody] AddBillingInformationDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid input data",
                    Data = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
                });
            }

            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.AddBillingInformation(model, GlobalVariables.Subdomain, CurrentUserId?.ToString());

            return response.ResponseCode switch
            {
                "201" => Created("", response),
                "400" => BadRequest(response),
                "404" => NotFound(response),
                "409" => Conflict(response),
                _ => StatusCode(500, response)
            };
        }

        /// <summary>
        /// Update billing information for the company
        /// </summary>
        /// <param name="model">Updated billing information data</param>
        /// <returns></returns>
        [HttpPut]
        [Route("update-billing-information")]
        public async Task<IActionResult> UpdateBillingInformation([FromBody] UpdateBillingInformationDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid input data",
                    Data = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)
                });
            }

            GlobalVariables.LoggedInUserId = CurrentUserId?.ToString();
            var response = await this.Services_Repo.AdminService.UpdateBillingInformation(model, GlobalVariables.Subdomain, CurrentUserId?.ToString());

            return response.ResponseCode switch
            {
                "200" => Ok(response),
                "400" => BadRequest(response),
                "404" => NotFound(response),
                _ => StatusCode(500, response)
            };
        }

        /// <summary>
        /// Get billing information for the company
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("get-billing-information")]
        public async Task<IActionResult> GetBillingInformation()
        {
            var response = await this.Services_Repo.AdminService.GetBillingInformation(GlobalVariables.Subdomain);

            return response.ResponseCode switch
            {
                "200" => Ok(response),
                "404" => NotFound(response),
                _ => StatusCode(500, response)
            };
        }

        #endregion
    }
}
