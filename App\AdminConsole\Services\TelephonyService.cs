using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.SignalR;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.AdminConsole.Dto;
using Jobid.App.AdminConsole.Services;
using Jobid.App.AdminConsole.Hubs;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers;
using Twilio.TwiML;
using Twilio.TwiML.Voice;
using Task = System.Threading.Tasks.Task;

namespace Jobid.App.AdminConsole.Services
{
    public class TelephonyService : ITelephonyService
    {
        private readonly ITwilioConferenceService _twilioConferenceService;
        private readonly IHubContext<CallHub> _hubContext;
        private readonly ILogger<TelephonyService> _logger;
        private readonly JobProDbContext _context;
        private readonly IPhoneNumberService _phoneNumberService;

        public TelephonyService(
            ITwilioConferenceService twilioConferenceService,
            IHubContext<CallHub> hubContext,
            ILogger<TelephonyService> logger,
            JobProDbContext context,
            IPhoneNumberService phoneNumberService)
        {
            _twilioConferenceService = twilioConferenceService;
            _hubContext = hubContext;
            _logger = logger;
            _context = context;
            _phoneNumberService = phoneNumberService;
        }

        public async Task<GenericResponse> InitiateCallAsync(InitiateCallSessionDto request)
        {
            try
            {
                _logger.LogInformation($"Initiating call session for user: {request.UserId}");

                // Create conference using simplified Twilio interface
                var conferenceName = $"Call_{DateTime.UtcNow:yyyyMMddHHmmss}_{request.UserId}";
                var conferenceId = await _twilioConferenceService.CreateConference(conferenceName, request.MaxParticipants);

                if (string.IsNullOrEmpty(conferenceId))
                {
                    _logger.LogError("Failed to create conference");
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to create call session",
                        Data = null
                    };
                }

                // Add PSTN participant for the outbound call
                var callSid = await _twilioConferenceService.AddPstnParticipant(conferenceName, request.ToNumber, request.FromNumberId.ToString());

                // Generate web access token for the caller
                var webToken = _twilioConferenceService.GenerateWebAccessToken(
                    request.UserDisplayName ?? $"user_{request.UserId}",
                    conferenceName,
                    60);

                // Notify via SignalR
                await NotifyParticipantsAsync(conferenceId, "call_session_initiated", new
                {
                    SessionId = conferenceId,
                    ConferenceName = conferenceName,
                    FromNumber = request.FromNumberId.ToString(),
                    ToNumber = request.ToNumber,
                    CallerName = request.UserDisplayName,
                    Timestamp = DateTime.UtcNow
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call session initiated successfully",
                    Data = new CallSessionDto
                    {
                        SessionId = conferenceId,
                        LiveKitToken = webToken, // Using Twilio token instead
                        LiveKitUrl = "twilio-conference", // Placeholder
                        TwilioCallSid = callSid,
                        FromNumber = request.FromNumberId.ToString(),
                        ToNumber = request.ToNumber,
                        Status = "initiated",
                        CreatedAt = DateTime.UtcNow,
                        Participants = new CallParticipantDto[]
                        {
                            new CallParticipantDto
                            {
                                UserId = request.UserId,
                                DisplayName = request.UserDisplayName,
                                Role = "initiator",
                                JoinedAt = DateTime.UtcNow,
                                IsConnected = true
                            }
                        }
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error initiating call session for user {request.UserId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while initiating the call",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> AnswerCallAsync(AnswerCallDto request)
        {
            try
            {
                _logger.LogInformation($"User answering call: {request.CallSid}");

                // Generate access token for answering participant
                var accessToken = _twilioConferenceService.GenerateWebAccessToken(
                    request.UserDisplayName ?? $"answerer_{request.UserId}",
                    request.LiveKitRoomName,
                    60);

                if (string.IsNullOrEmpty(accessToken))
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Failed to generate access token for call"
                    };
                }

                // Add web participant to conference
                await _twilioConferenceService.AddWebParticipant(request.LiveKitRoomName, request.UserDisplayName ?? $"answerer_{request.UserId}", request.UserDisplayName);

                // Notify other participants
                await NotifyParticipantsAsync(request.LiveKitRoomName, "call_answered", new
                {
                    AnsweredBy = request.UserDisplayName,
                    UserId = request.UserId,
                    CallSid = request.CallSid,
                    Timestamp = DateTime.UtcNow
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call answered successfully",
                    Data = new
                    {
                        AccessToken = accessToken,
                        SessionId = request.LiveKitRoomName,
                        CallSid = request.CallSid
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error answering call {request.CallSid}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while answering the call",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> EndCallAsync(EndCallDto request)
        {
            try
            {
                _logger.LogInformation($"Ending call session: {request.SessionId}");

                await _twilioConferenceService.EndConference(request.SessionId);

                // Notify all participants
                await NotifyParticipantsAsync(request.SessionId, "call_ended", new
                {
                    SessionId = request.SessionId,
                    EndedBy = request.UserId,
                    Reason = request.Reason ?? "user_ended",
                    Timestamp = DateTime.UtcNow
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call ended successfully",
                    Data = new { SessionId = request.SessionId }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error ending call {request.SessionId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while ending the call",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> GetCallSessionAsync(string sessionId)
        {
            try
            {
                var conferenceInfo = await _twilioConferenceService.GetConference(sessionId);

                return new GenericResponse
                {
                    ResponseCode = conferenceInfo != null ? "200" : "404",
                    ResponseMessage = conferenceInfo != null ? "Call session found" : "Call session not found",
                    Data = conferenceInfo
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting call session {sessionId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while getting call session",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> AddParticipantAsync(TelephonyAddParticipantDto request)
        {
            try
            {
                _logger.LogInformation($"Adding participant to session: {request.SessionId}");

                string result;

                if (request.IsPhoneNumber && !string.IsNullOrEmpty(request.PhoneNumber))
                {
                    // Add PSTN participant
                    result = await _twilioConferenceService.AddPstnParticipant(request.SessionId, request.PhoneNumber, request.FromNumber);
                }
                else
                {
                    // Add web participant
                    result = await _twilioConferenceService.AddWebParticipant(request.SessionId, request.ParticipantId, request.ParticipantName);
                }

                if (!string.IsNullOrEmpty(result))
                {
                    // Notify other participants
                    await NotifyParticipantsAsync(request.SessionId, "participant_added", new
                    {
                        ParticipantName = request.ParticipantName,
                        PhoneNumber = request.PhoneNumber,
                        AddedBy = request.AddedBy,
                        Timestamp = DateTime.UtcNow
                    });

                    // Generate token for web participants
                    string accessToken = null;
                    if (!request.IsPhoneNumber)
                    {
                        accessToken = _twilioConferenceService.GenerateWebAccessToken(request.ParticipantId, request.SessionId, 60);
                    }

                    return new GenericResponse
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Participant added successfully",
                        Data = new
                        {
                            AccessToken = accessToken,
                            SessionId = request.SessionId,
                            ParticipantId = result
                        }
                    };
                }

                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Failed to add participant"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error adding participant to session {request.SessionId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while adding participant",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> RemoveParticipantAsync(TelephonyRemoveParticipantDto request)
        {
            try
            {
                _logger.LogInformation($"Removing participant from session: {request.SessionId}");

                await _twilioConferenceService.RemoveParticipant(request.SessionId, request.ParticipantId);

                // Notify other participants
                await NotifyParticipantsAsync(request.SessionId, "participant_removed", new
                {
                    ParticipantId = request.ParticipantId,
                    RemovedBy = request.RemovedBy,
                    Reason = request.Reason,
                    Timestamp = DateTime.UtcNow
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Participant removed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error removing participant from session {request.SessionId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while removing participant",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> ForwardCallAsync(ForwardCallDto request)
        {
            try
            {
                _logger.LogInformation($"Forwarding call from session: {request.SessionId}");

                if (request.IsTransfer)
                {
                    // Transfer call - disconnect original caller and connect to new destination
                    if (!string.IsNullOrEmpty(request.ToNumber))
                    {
                        // Create new conference for transfer
                        var transferConferenceName = $"Transfer_{DateTime.UtcNow:yyyyMMddHHmmss}_{request.ForwardedBy}";
                        var transferConferenceId = await _twilioConferenceService.CreateConference(transferConferenceName, 10);

                        if (!string.IsNullOrEmpty(transferConferenceId))
                        {
                            // Add new participant to transfer conference
                            var transferCallSid = await _twilioConferenceService.AddPstnParticipant(
                                transferConferenceName,
                                request.ToNumber,
                                request.FromNumber);

                            // End original session
                            await _twilioConferenceService.EndConference(request.SessionId);

                            // Notify participants
                            await NotifyParticipantsAsync(request.SessionId, "call_transferred", new
                            {
                                TransferredBy = request.ForwardedBy,
                                ToNumber = request.ToNumber,
                                ToDisplayName = request.ToDisplayName,
                                NewSessionId = transferConferenceId,
                                Reason = request.Reason,
                                Timestamp = DateTime.UtcNow
                            });

                            return new GenericResponse
                            {
                                ResponseCode = "200",
                                ResponseMessage = "Call transferred successfully",
                                Data = new
                                {
                                    NewSessionId = transferConferenceId,
                                    TransferCallSid = transferCallSid,
                                    ToNumber = request.ToNumber
                                }
                            };
                        }
                    }
                }
                else
                {
                    // Conference call - add new participant to existing session
                    if (!string.IsNullOrEmpty(request.ToNumber))
                    {
                        var forwardCallSid = await _twilioConferenceService.AddPstnParticipant(
                            request.SessionId,
                            request.ToNumber,
                            request.FromNumber);

                        // Notify participants
                        await NotifyParticipantsAsync(request.SessionId, "participant_forwarded", new
                        {
                            ForwardedBy = request.ForwardedBy,
                            ToNumber = request.ToNumber,
                            ToDisplayName = request.ToDisplayName,
                            CallSid = forwardCallSid,
                            Reason = request.Reason,
                            Timestamp = DateTime.UtcNow
                        });

                        return new GenericResponse
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Call forwarded to conference successfully",
                            Data = new
                            {
                                SessionId = request.SessionId,
                                ForwardCallSid = forwardCallSid,
                                ToNumber = request.ToNumber
                            }
                        };
                    }
                }

                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invalid forward request - missing destination number"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error forwarding call from session {request.SessionId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while forwarding the call",
                    DevResponseMessage = ex.Message
                };
            }
        }

        // Remaining interface methods with placeholder implementations
        public async Task<GenericResponse> GetActiveBridgeSessionsAsync()
        {
            return await Task.FromResult(new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "No bridge sessions in Twilio-only mode",
                Data = new object[0]
            });
        }

        public async Task<GenericResponse> StopBridgeSessionAsync(string sessionId)
        {
            return await Task.FromResult(new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "No bridge sessions to stop in Twilio-only mode"
            });
        }

        public async Task<GenericResponse> HandleTwilioCallStatusAsync(TwilioCallStatusDto request, Guid callId, string subdomain)
        {
            try
            {
                _logger.LogInformation($"Handling Twilio call status: {request.CallStatus} for call {request.CallSid}");

                // Update the call record with the call status
                var recordUpdateRes = await _phoneNumberService.UpdateCallStatusByCallId(callId, request.CallStatus, subdomain);
                if (recordUpdateRes.ResponseCode != "200")
                {
                    _logger.LogError($"No record found for {callId}");
                    return recordUpdateRes;
                }

                // Process call status update
                await NotifyParticipantsAsync(request.CallSid, "call_status_update", new
                {
                    CallSid = request.CallSid,
                    Status = request.CallStatus,
                    Direction = request.Direction,
                    From = request.From,
                    To = request.To,
                    Timestamp = request.Timestamp
                });

                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = "Call status processed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling call status for {request.CallSid}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "Error processing call status",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<string> HandleInboundCallAsync(string from, string to, string callSid)
        {
            try
            {
                _logger.LogInformation($"Handling inbound call from {from} to {to}");

                // Create conference for inbound call
                var conferenceName = $"Inbound_{callSid}_{DateTime.UtcNow:HHmmss}";
                var conferenceId = await _twilioConferenceService.CreateConference(conferenceName, 10);

                if (!string.IsNullOrEmpty(conferenceId))
                {
                    // Notify available agents
                    await NotifyParticipantsAsync("inbound_calls", "incoming_call", new
                    {
                        From = from,
                        To = to,
                        CallSid = callSid,
                        ConferenceId = conferenceId,
                        ConferenceName = conferenceName,
                        Timestamp = DateTime.UtcNow
                    });

                    var response = new VoiceResponse();
                    var dial = new Dial();
                    dial.Conference(conferenceName);
                    response.Append(dial);

                    return response.ToString();
                }

                var errorResponse = new VoiceResponse();
                errorResponse.Say("Sorry, we are unable to take your call right now. Please try again later.", voice: Say.VoiceEnum.Alice);

                return errorResponse.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error handling inbound call from {from}");

                var errorResponse = new VoiceResponse();
                errorResponse.Say("We are experiencing technical difficulties. Please try again later.", voice: Say.VoiceEnum.Alice);

                return errorResponse.ToString();
            }
        }

        // Call History and Analytics implementations
        public async Task<GenericResponse> GetCallHistoryAsync(Guid phoneNumberId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                _logger.LogInformation($"Getting call history for phone number: {phoneNumberId}");

                // Delegate to phone number service which has the actual implementation
                var result = await _phoneNumberService.GetCallHistory(phoneNumberId, startDate, endDate);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting call history for phone number {phoneNumberId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving call history",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> GetCallRecordingAsync(string callId)
        {
            try
            {
                _logger.LogInformation($"Getting call recording for call: {callId}");

                // Delegate to phone number service which has the actual implementation
                var result = await _phoneNumberService.GetCallRecording(callId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting call recording for call {callId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving call recording",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> GetCallTranscriptionAsync(string callId)
        {
            try
            {
                _logger.LogInformation($"Getting call transcription for call: {callId}");

                // Delegate to phone number service which has the actual implementation
                var result = await _phoneNumberService.GetCallTranscription(callId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting call transcription for call {callId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving call transcription",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public async Task<GenericResponse> GetCallAnalyticsAsync(Guid phoneNumberId, DateTime? startDate, DateTime? endDate)
        {
            try
            {
                _logger.LogInformation($"Getting call analytics for phone number: {phoneNumberId}");

                // Delegate to phone number service which has the actual implementation
                var result = await _phoneNumberService.GetCallAnalytics(phoneNumberId, startDate, endDate);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error getting call analytics for phone number {phoneNumberId}");
                return new GenericResponse
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while retrieving call analytics",
                    DevResponseMessage = ex.Message
                };
            }
        }

        public string HandleDialResult(string dialCallStatus, string callSid, string requestScheme, string requestHost)
        {
            var response = new VoiceResponse();
            response.Say($"Call completed with status: {dialCallStatus}", voice: Say.VoiceEnum.Alice);

            return response.ToString();
        }

        public string GetQueueWaitMusic()
        {
            var response = new VoiceResponse();
            response.Play(new Uri("https://api.twilio.com/cowbell.mp3"), loop: 10);

            return response.ToString();
        }

        public string HandleVoicemail(string recordingUrl, string callSid, string from, string to)
        {
            var response = new VoiceResponse();
            response.Say("Thank you for your message. We will get back to you soon.", voice: Say.VoiceEnum.Alice);

            return response.ToString();
        }

        public string GetInboundCallFallback(string from, string to, string callSid, string requestScheme, string requestHost)
        {
            var response = new VoiceResponse();
            response.Say("All our agents are busy. Please leave a message after the beep.", voice: Say.VoiceEnum.Alice);
            response.Record(maxLength: 60, transcribe: true);

            return response.ToString();
        }

        private async Task NotifyParticipantsAsync(string groupId, string eventType, object data)
        {
            try
            {
                await _hubContext.Clients.Group(groupId).SendAsync("TelephonyEvent", new
                {
                    EventType = eventType,
                    Data = data,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"Failed to send SignalR notification for {eventType}");
            }
        }
    }
}
