using Microsoft.Extensions.DependencyInjection;
using WatchDog;

namespace Jobid.App.Helpers.Extensions
{
    public static class WatchDogServiceExtension
    {
        /// <summary>
        /// WatchDog Service Extension
        /// </summary>
        /// <param name="services"></param>
        /// <param name="connectionString"></param>
        public static void AddWatchDogService(this IServiceCollection services, string connectionString)
        {
            services.AddWatchDogServices(options =>
            {
                options.IsAutoClear = true;
                options.ClearTimeSchedule = WatchDog.src.Enums.WatchDogAutoClearScheduleEnum.Weekly;
                options.SetExternalDbConnString = connectionString;
                options.DbDriverOption = WatchDog.src.Enums.WatchDogDbDriverEnum.PostgreSql;
            });
        }
    }
}
