﻿using Jobid.App.Helpers;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Models;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.JobProjectManagement.Models;
using Jobid.App.JobProjectManagement.ViewModel;
using Jobid.App.Tenant.Contract;
using Microsoft.AspNetCore.Hosting;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web;
using static Jobid.App.JobProject.Enums.Enums;
using Team = Jobid.App.JobProjectManagement.Models.Team;
using static Jobid.App.Helpers.Utils.Extensions;
using Microsoft.AspNetCore.Http;
using Jobid.App.Calender.ViewModel;
using RestSharp;
using Jobid.App.JobProject.Services.Contract;
using Microsoft.Extensions.Configuration;
using Jobid.App.Helpers.Services.Contract;
using static Jobid.App.Subscription.Enums.Enums;

namespace Jobid.App.JobProject.Services.Implemetations
{
    public class TeamSheetService : ITeamSheetService
    {
        private JobProDbContext Db;
        public JobProDbContext Dbo;
        private IEmailService _emailService;
        private readonly ITenantService _tenantService;
        private readonly ICompanyUserInvite _companyUserInvite;
        private readonly IWebHostEnvironment _environment;
        private readonly IAWSS3Sevices _aWSS3Sevices;
        private readonly IApiCallService _apiCallService;
        private readonly int _freePlanDays;
        private readonly int _noOfFreeUsers;

        public TeamSheetService(JobProDbContext _db, JobProDbContext publicSchemaContext, IEmailService emailService, ITenantService tenantService, ICompanyUserInvite companyUserInvite, IWebHostEnvironment environment, IAWSS3Sevices aWSS3Sevices, IApiCallService apiCallService, IConfiguration configuration)
        {
            Db = _db;
            Dbo = publicSchemaContext;
            _emailService = emailService;
            _tenantService = tenantService;
            _companyUserInvite = companyUserInvite;
            _environment = environment;
            _aWSS3Sevices = aWSS3Sevices;
            _apiCallService = apiCallService;
            _freePlanDays = configuration.GetValue<int>("FreePlanDays");
            _noOfFreeUsers = configuration.GetValue<int>("NumberOfFreeUsers");
        }

        #region Create Team
        /// <summary>
        /// Crate Team
        /// </summary>
        /// <param name="teamDto"></param>
        /// <returns></returns>
        public async Task<Team> AddTeam(TeamDto teamDto)
        {
            // Cheack if a team with the name already exist
            var teamExist = await Db.Teams.FirstOrDefaultAsync(x => x.Name.ToLower() == teamDto.Name.ToLower());
            if (teamExist != null)
                throw new RecordAlreadyExistException("Team with the name already exist");

            var tenant = await _tenantService.GetTenantBySubdomain(teamDto.Subdomain);

            // Get the invitee name using the user id
            var inviteeDetails = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == teamDto.UserId);
            var invitee = inviteeDetails?.FirstName + " " + inviteeDetails?.LastName;

            var team = new Team
            {
                Name = teamDto.Name,
                CreatedBy = teamDto.UserId,
                UpdatedBy = teamDto.UserId
            };
            await Db.Teams.AddAsync(team);

            // Add Team Members
            bool hasTeamMembers = teamDto.TeamMemberIds != null && teamDto.TeamMemberIds.Any();
            if (hasTeamMembers)
            {
                var teamMembers = new List<TeamMembers>();
                foreach (var item in teamDto.TeamMemberIds)
                {
                    var teamMember = new TeamMembers
                    {
                        TeamId = team.Id,
                        UserId = item,
                    };
                    teamMembers.Add(teamMember);
                }

                await Db.TeamMembers.AddRangeAsync(teamMembers);
            }
            try
            {
                var res = await Db.SaveChangesAsync();

                if (res > 0 && hasTeamMembers)
                {
                    var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/team-member-added-email.html");
                    var template = File.ReadAllText(templatePath);

                    foreach (var memberId in teamDto.TeamMemberIds)
                    {
                        // Get user details and send an email notification
                        var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == memberId);
                        template = template.Replace("{jobpro}", tenant.CompanyName).Replace("{firstname}", user?.FirstName).Replace("{lastname}", user?.LastName).Replace("{url}", Utility.Constants.FRONT_END_DASHBOARD_URL).Replace("{invitee}", invitee);

                        var subject = "Team Addition";

                        await _emailService.SendEmail(user.Email, subject, template);
                    }
                }

                return res > 0 ? team : null;
            }
            catch
            {
                throw;
            }
        }
        #endregion

        #region Update Team
        /// <summary>
        /// Update Team
        /// </summary>
        /// <param name="teamDto"></param>
        /// <param name="teamId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> UpdateTeam(TeamDto teamDto, string teamId)
        {
            // Cheack if a team with the name already exist
            var teamExist = await Db.Teams.FirstOrDefaultAsync(x => x.Name.ToLower() == teamDto.Name.ToLower());
            if (teamExist != null)
                throw new RecordAlreadyExistException("Team with the name already exist");

            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            team.Name = teamDto.Name;
            team.UpdatedBy = teamDto.UserId;
            team.UpdatedAt = GetAdjustedDateTimeBasedOnTZNow();

            Db.Teams.Update(team);
            var res = await Db.SaveChangesAsync();
            return res > 0 ? true : false;
        }
        #endregion

        #region Delete Team
        /// <summary>
        /// Delete Team
        /// </summary>
        /// <param name="teamId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> DeleteTeam(string teamId)
        {
            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            // Delete Team members
            var teamMembers = Db.TeamMembers.Where(x => x.TeamId.ToString() == teamId).ToList();
            if (teamMembers.Any())
            {
                Db.TeamMembers.RemoveRange(teamMembers);
            }

            Db.Teams.Remove(team);
            var res = await Db.SaveChangesAsync();
            return res > 0 ? true : false;
        }
        #endregion

        #region Get Team By Id
        /// <summary>
        /// Get Team By Id
        /// </summary>
        /// <param name="teamId"></param>
        /// <returns></returns>
        public async Task<TeamVm> GetTeamById(string teamId)
        {
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            // Get Team Members and role counts
            var adminCount = 0;
            var teamMemberCount = 0;
            var teamLeadCount = 0;

            var teamMembers = await Db.TeamMembers.Where(x => x.TeamId.ToString() == teamId && x.Email == null).ToListAsync();
            var teamMembersWithEmails = await Db.TeamMembers.Where(x => x.TeamId.ToString() == teamId && x.UserId == null).ToListAsync();

            // Get invited members that have accepted the invite by checking the userprofiles table
            foreach (var item in teamMembersWithEmails)
            {
                var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == item.Email);
                if (user != null)
                {
                    teamMembers.Add(item);
                }
            }

            var teamMemberDetails = new List<UserDto>();
            foreach (var member in teamMembers)
            {
                switch (member.Role)
                {
                    case DataSeeder.Admin:
                        adminCount++;
                        break;
                    case DataSeeder.TeamMember:
                        teamMemberCount++;
                        break;
                    case DataSeeder.SuperAdmin:
                        teamLeadCount++;
                        break;
                    default:
                        break;
                }

                var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == member.UserId);
                if (user != null)
                {
                    teamMemberDetails.Add(new UserDto
                    {
                        Id = user.UserId,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        MiddleName = user.MiddleName,
                    });
                }
            }

            var teamVm = new TeamVm
            {
                Team = team,
                AdminCount = adminCount,
                TeamLeadCount = teamLeadCount,
                TeamMemberCount = teamMemberCount,
                Members = teamMemberDetails
            };

            return teamVm;
        }
        #endregion

        #region Get All Teams
        /// <summary>
        /// Get All Teams
        /// </summary>
        /// <returns></returns>
        public async Task<List<TeamVm>> GetAllTeams()
        {
            var teams = await Db.Teams.ToListAsync();

            var teamVms = new List<TeamVm>();
            foreach (var team in teams)
            {
                // Get Team Members and role counts
                var adminCount = 0;
                var teamMemberCount = 0;
                var teamLeadCount = 0;

                var teamMembers = await Db.TeamMembers.Where(x => x.TeamId == team.Id && x.Email == null).ToListAsync();
                var teamMembersWithEmails = await Db.TeamMembers.Where(x => x.TeamId == team.Id && x.UserId == null).ToListAsync();

                // Get invited members that have accepted the invite by checking the userprofiles table
                foreach (var item in teamMembersWithEmails)
                {
                    var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == item.Email);
                    if (user != null)
                    {
                        teamMembers.Add(item);
                    }
                }

                var teamMemberDetails = new List<UserDto>();
                foreach (var member in teamMembers)
                {
                    switch (member.Role)
                    {
                        case "Admin":
                            adminCount++;
                            break;
                        case "Team Member":
                            teamMemberCount++;
                            break;
                        case "Team Lead":
                            teamLeadCount++;
                            break;
                        default:
                            break;
                    }
                    var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == member.UserId);
                    if (user != null)
                    {
                        teamMemberDetails.Add(new UserDto
                        {
                            Id = user.UserId,
                            Email = user.Email,
                            FirstName = user.FirstName,
                            LastName = user.LastName,
                            MiddleName = user.MiddleName,
                            Role = member.Role,
                            ProfileUrl = user.ProfilePictureUrl != null ? await _aWSS3Sevices.GetSignedUrlAsync(user.ProfilePictureUrl) : null
                        });
                    }
                }
                var teamVm = new TeamVm
                {
                    Team = team,
                    AdminCount = adminCount,
                    TeamLeadCount = teamLeadCount,
                    TeamMemberCount = teamMemberCount,
                    Members = teamMemberDetails
                };
                teamVms.Add(teamVm);
            }

            return teamVms;
        }
        #endregion

        #region Get All User Teams
        /// <summary>
        /// Get All Teams user belongs to
        /// </summary>
        /// <returns></returns>
        public async Task<List<Team>> GetAllUserTeams(string UserId)
        {
            var teamIds = await Db.TeamMembers.Where(x => x.UserId == UserId).Select(x => x.TeamId).ToListAsync();

            var teams = new List<Team>();
            foreach (var id in teamIds)
            {
                // Get Team  and add into team array
                var team = await Db.Teams.Where(x => x.Id == id).FirstOrDefaultAsync();
                teams.Add(team);
            }

            return teams;
        }
        #endregion

        #region Get Team Members
        /// <summary>
        /// Get Team Members
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="filters"></param>
        /// <returns></returns>
        public async Task<GenericResponse> GetTeamMembers(string teamId, GetAllTeamMembersFilter filters)
        {
            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            var teamMembers = await Db.TeamMembers.Where(x => x.TeamId.ToString() == teamId).ToListAsync();

            // Apply filters
            if (filters.MemberId is not null)
                teamMembers = teamMembers.Where(teamMember => teamMember.UserId == teamId).ToList();
            else if (filters.ProjectIds.Any() && filters.ProjectIds[0] is not null && filters.RoleId is null)
            {
                var listOfProjectMemberIds = await Db.projectMgmt_ProjectUsers
                    .Where(project => filters.ProjectIds.Contains(project.ProjectMgmt_ProjectId.ToString()))
                    .Select(pro => pro.UserId).ToListAsync();

                teamMembers = teamMembers.Where(teamMember => listOfProjectMemberIds.Any(userId => userId == teamMember.UserId))
                    .ToList();
            }
            else if (filters.ProjectIds.Any() && filters.ProjectIds[0] is not null && filters.RoleId is not null)
            {
                var listOfProjectMemberIds = await Db.projectMgmt_ProjectUsers
                    .Where(project => filters.ProjectIds.Contains(project.ProjectMgmt_ProjectId.ToString()))
                    .Select(pro => pro.UserId).ToListAsync();

                teamMembers = teamMembers.Where(teamMember => listOfProjectMemberIds.Any(userId => userId == teamMember.UserId))
                    .ToList();

                // Filter by role Id
                var usersThatBelongToRoleIds = await Db.UserAndRoleIds
                    .Where(ur => ur.RoleId == filters.RoleId)
                    .Select(u => u.UserProId).Distinct().ToListAsync();

                teamMembers = teamMembers
                    .Where(teamMember => usersThatBelongToRoleIds
                        .Any(roleUser => roleUser == teamMember.UserId))
                    .ToList();
            }
            else if (filters.ProjectIds.Any() && filters.ProjectIds[0] is null && filters.RoleId is not null)
            {
                // Filter by role Id
                var usersThatBelongToRoleIds = await Db.UserAndRoleIds
                    .GroupBy(
                        role => role.RoleId,
                        user => user.UserProId)
                    .Select(group => new
                    {
                        DistinctUserProIds = group.Select(userId => userId).Distinct() // Get distinct UserProIds within each group
                    }).ToListAsync();

                teamMembers = teamMembers
                    .Where(teamMember => usersThatBelongToRoleIds
                        .Any(roleUser => roleUser.DistinctUserProIds.Contains(teamMember.UserId)))
                    .ToList();
            }

            var teamMemberDetails = new List<GetTeamMembersForTHDto>();
            foreach (var member in teamMembers)
            {
                var user = new UserProfile();
                if (member.UserId != null)
                    user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == member.UserId);
                else
                    user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == member.Email);

                if (user == null)
                    continue;

                var userDto = new UserDto
                {
                    Id = user.UserId,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    MiddleName = user.MiddleName,
                    ProfileUrl = user.ProfilePictureUrl,
                };

                userDto.ProfileUrl = await _aWSS3Sevices.GetSignedUrlAsync(userDto.ProfileUrl);

                // Get the projects the user is assigned to
                var projects = await Db.projectMgmt_ProjectUsers
                    .Include(x => x.projectMgmt_Project)
                    .Where(x => x.UserId == user.Id)
                    .Select(z => z.projectMgmt_Project.Name)
                    .ToListAsync();

                teamMemberDetails.Add(new GetTeamMembersForTHDto
                {
                    Users = userDto,
                    Projects = projects
                });
            }
            return new GenericResponse
            {
                ResponseMessage = "Team members retrieved successfully",
                ResponseCode = "200",
                Data = teamMemberDetails
            };
        }
        #endregion

        #region Add Internal Team Members
        /// <summary>
        /// Add Internal Team Members
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="memberIds"></param>
        /// <param name="loggedInUserId"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> AddInternalTeamMembers(string teamId, List<string> memberIds, string subdomain, string loggedInUserId)
        {
            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            // Get the invitee name
            var invitee = Db.UserProfiles.Where(x => x.UserId == loggedInUserId)
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            var count = 0;
            foreach (var memberId in memberIds)
            {
                // Check if user is already a member of the team
                var teamMember = await Db.TeamMembers.FirstOrDefaultAsync(x => x.TeamId.ToString() == teamId && x.UserId == memberId);
                if (teamMember == null)
                {
                    // Get user details and send an email notification
                    var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == memberId);
                    // Send an email notification
                    var encodedEmail = HttpUtility.UrlEncode(user.Email);
                    var url = string.Format(Utility.Constants.FRONT_END_DASHBOARD_URL, subdomain) + $"?type={TypeForInviteUrl.Team.ToString()}&id={team.Id}";

                    var templatePath = Path.Combine(_environment.WebRootPath, @"EmailTemplates/team-member-added-email.html");
                    var template = File.ReadAllText(templatePath);

                    template = template.Replace("{firstname}", user.FirstName).Replace("{lastname}", user.LastName).Replace("{invitee}", invitee)
                    .Replace("{url}", url).Replace("{team}", team.Name);
                    var subject = "Team Addition";

                    await _emailService.SendEmail(template, user.Email, subject);

                    // Add user to team
                    var newTeamMember = new TeamMembers
                    {
                        TeamId = Guid.Parse(teamId),
                        UserId = memberId,
                    };
                    await Db.TeamMembers.AddAsync(newTeamMember);
                }
                else
                    count++;
            }

            if (count == memberIds.Count)
                return true;

            var res = await Db.SaveChangesAsync();
            return res > 0 ? true : false;
        }
        #endregion

        #region Add External Team Members
        /// <summary>
        /// Add External Team Members
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> AddExternalTeamMembers(AddExternalTeamMembersToTHDto model)
        {

            // Check if an external user can be invited to the company
            var companyId = Dbo.Tenants.Where(x => x.Subdomain == model.SubDomain).Select(c => c.Id).FirstOrDefaultAsync().Result;
            var usersWithPermission = Db.AppPermissions.Where(x => x.Application == Applications.Joble.ToString()).CountAsync().Result;
            var numberOfInvites = Dbo.CompanyUserInvites.Where(x => x.TenantId == companyId.ToString() && x.Application == Applications.Joble)
                    .CountAsync().Result;
            var subscriptionFor = Dbo.Subscriptions.Where(x => x.TenantId == companyId && x.Application == Applications.Joble).Select(c => c.SubscriptionFor).FirstOrDefaultAsync().Result;

            if (subscriptionFor == numberOfInvites + 1)
            {
                throw new InvalidOperationException("External users cannot be invited at ths time, please contact your admin");
            }

            // Get TenantId
            var tenant = await _tenantService.GetTenantBySubdomain(model.SubDomain);
            var tenantId = tenant.Id;

            // Get the invitee name
            var invitee = Dbo.Users.Where(x => x.Id == model.UserId.ToString())
                .Select(x => x.FirstName + " " + x.LastName)
                .FirstOrDefault();

            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == model.TeamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");

            //var templatePath = Path.Combine(_environment.ContentRootPath, @"App\Helpers\EmailTemplates\team-invite-email.html");
            //var template = File.ReadAllText(templatePath);

            var template = await _emailService.GetExternalTeamMemberMailTemplate();

            foreach (var email in model.ExternalMemberEmails)
            {
                // Check if user is already a member of the team
                var teamMember = await Db.TeamMembers.FirstOrDefaultAsync(x => x.TeamId.ToString() == model.TeamId && x.Email == email);
                if (teamMember == null)
                {
                    // Add user to team
                    var newTeamMember = new TeamMembers
                    {
                        TeamId = Guid.Parse(model.TeamId),
                        Email = email,
                    };
                    await Db.TeamMembers.AddAsync(newTeamMember);
                }

                // Add an invite to the database
                var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(new CompanyUserInviteVM()
                {
                    Email = email,
                    Application = Applications.Joble,
                }, tenantId.ToString());

                // Add user to the team
                var member = new TeamMembers
                {
                    TeamId = Guid.Parse(model.TeamId),
                    Email = email,
                };
                await Db.TeamMembers.AddAsync(member);

                // Send an email notification
                var encodedEmail = HttpUtility.UrlEncode(email);
                var inviteUrl = string.Format(Utility.Constants.INVITE_URL, model.SubDomain, invitee, tenantId, Applications.Joble, encodedEmail);

                template = template.Replace("{Jobpro}", tenant.CompanyName).Replace("{message}", $"and you have been added to {team.Name} team").Replace("{name}", invitee)
                    .Replace("{url}", inviteUrl);
                var subject = "Team Invitation";

                await _emailService.SendEmail(email, subject, template);
            }

            var res = await Db.SaveChangesAsync();
            return res > 0 ? true : false;
        }
        #endregion

        #region Remove Team Member
        /// <summary>
        /// Remove Team Member
        /// </summary>
        /// <param name="teamId"></param>
        /// <param name="userIds"></param>
        /// <returns></returns>
        /// <exception cref="RecordNotFoundException"></exception>
        public async Task<bool> RemoveTeamMember(string teamId, List<string> userIds)
        {
            // Check if team exist
            var team = await Db.Teams.FirstOrDefaultAsync(x => x.Id.ToString() == teamId);
            if (team == null)
                throw new RecordNotFoundException("Team not found");
            var teamMembers = new List<TeamMembers>();
            foreach (var userId in userIds)
            {
                // Check if user is a member of the team
                var teamMember = await Db.TeamMembers.FirstOrDefaultAsync(x => x.TeamId.ToString() == teamId && x.UserId == userId);
                if (teamMember != null)
                    teamMembers.Add(teamMember);
            }

            if (teamMembers.Count == 0)
                throw new RecordNotFoundException("Team member not found");

            Db.TeamMembers.RemoveRange(teamMembers);
            var res = await Db.SaveChangesAsync();
            return res > 0 ? true : false;
        }
        #endregion

        #region Get Pending Invitations
        /// <summary>
        /// Get Pending Invitations
        /// </summary>
        /// <param name="tenantId"></param>
        /// <param name="paginationParameters"></param>
        /// <returns></returns>
        public async Task<List<CompanyUserInviteVM>> GetPendingInvitations(string tenantId, PaginationParameters paginationParameters)
        {
            var invites = await _companyUserInvite.GetInvites(tenantId, paginationParameters);
            return invites;
        }
        #endregion

        #region Team Compositions
        /// <summary>
        /// Get Team Composition
        /// </summary>
        /// <returns></returns>
        public async Task<GenericResponse> GetTeamComposition()
        {
            var teams = await Db.Teams.ToListAsync();
            if (!teams.Any())
                return new GenericResponse { ResponseCode = "400", ResponseMessage = "No team found" };

            var teamComposition = new List<TeamCompositionDto>();
            var membersCount = 0;
            var totalMembersCount = 0;

            foreach (var team in teams)
            {
                var teamMembers = await Db.TeamMembers.Where(x => x.TeamId == team.Id && x.Email == null).ToListAsync();
                var teamMembersWithEmails = await Db.TeamMembers.Where(x => x.TeamId == team.Id && x.UserId == null).ToListAsync();

                // Get invited members that have accepted the invite by checking the userprofiles table
                foreach (var item in teamMembersWithEmails)
                {
                    var user = await Db.UserProfiles.FirstOrDefaultAsync(x => x.Email == item.Email);
                    if (user != null)
                    {
                        teamMembers.Add(item);
                    }
                }

                membersCount = teamMembers.Count;
                totalMembersCount += membersCount;
                var teamCompositionDto = new TeamCompositionDto
                {
                    TeamName = team.Name,
                    MembersCount = membersCount,
                };

                teamComposition.Add(teamCompositionDto);
            }

            return new GenericResponse
            {
                ResponseMessage = "Team composition retrieved successfully",
                ResponseCode = "200",
                Data = new
                {
                    TeamComposition = teamComposition,
                    TotalMembersCount = totalMembersCount
                }
            };
        }
        #endregion

        #region Bulk Invite
        /// <summary>
        /// Bulk Invite
        /// </summary>
        /// <param name="file"></param>
        /// <param name="subdomain"></param>
        /// <param name="userId"></param>
        /// <returns></returns>
        /// <exception cref="DirtyFormException"></exception>
        public async Task<GenericResponse> ProcessBulkInvite(IFormFile file, string subdomain, string userId, string token)
        {
            if (file == null || file.Length == 0)
            {
                throw new DirtyFormException("File is empty or not provided.");
            }

            // Determine the file type based on extension
            var fileExtension = Path.GetExtension(file.FileName)?.ToLower();
            List<BulkInviteDto> invites;

            if (fileExtension == ".xlsx" || fileExtension == ".xls")
            {
                // Process as Excel file
                invites = await GenericMethods.ProcessExcelFileAsync(file, record =>
                {
                    var email = record[0]?.ToString().Trim();
                    var app = record[1]?.ToString().Trim();

                    if (string.IsNullOrEmpty(email))
                    {
                        return null;
                    }

                    return new BulkInviteDto
                    {
                        Email = email,
                        Application = app
                    };
                });
            }
            else if (fileExtension == ".csv")
            {
                // Process as CSV file
                invites = await GenericMethods.ProcessCsvFileAsync(file, columns =>
                {
                    if (columns.Length < 2) return null;

                    var email = columns[0]?.Trim();
                    var app = columns[1]?.Trim();

                    if (string.IsNullOrEmpty(email))
                    {
                        return null;
                    }

                    return new BulkInviteDto
                    {
                        Email = email,
                        Application = app
                    };
                });
            }
            else
            {
                throw new DirtyFormException("Unsupported file format. Please upload an Excel or CSV file.");
            }

            return await InviteExternalUserToCompany(invites, subdomain, userId, false, token);
        }

        #endregion

        #region Invite An External Users to a Company
        /// <summary>
        /// Invite an external users to a company
        /// </summary>
        /// <param name="invites"></param>
        /// <param name="subdomain"></param>
        /// <param name="userId"></param>
        /// <param name="resendInvite"></param>
        /// <returns></returns>
        public async Task<GenericResponse> InviteExternalUserToCompany(List<BulkInviteDto> invites, string subdomain, string userId, bool resendInvite, string token)
        {
            try
            {
                var company = await Dbo.Tenants.FirstOrDefaultAsync(x => x.Subdomain.ToLower() == subdomain.ToLower());
                if (company == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Company not found",
                    };
                }

                // Get all secondary domains at once
                var secDomains = await Dbo.SecDomains
                    .Where(x => x.TenantId == company.Id)
                    .Select(d => d.Domain.ToLower())
                    .ToListAsync();
                
                // Add primary domain
                secDomains.Add(company.VerifiedEmailDomain?.ToLower());
                
                var invitee = await Db.UserProfiles.FirstOrDefaultAsync(x => x.UserId == userId);
                if (invitee == null)
                {
                    return new GenericResponse
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Inviter not found",
                    };
                }
                
                foreach (var invite in invites)
                {
                    if (!invite.Email.Contains("@"))
                    {
                        return new GenericResponse
                        {
                            Data = invite.Email,
                            ResponseMessage = $"{invite.Email} is not a valid email address",
                            ResponseCode = "400"
                        };
                    }
                    
                    // Check if user already exists
                    var userExists = await Db.UserProfiles.AnyAsync(x => x.Email.ToLower() == invite.Email.ToLower());
                    if (userExists)
                    {
                        return new GenericResponse
                        {
                            Data = invite.Email,
                            ResponseMessage = $"Invitation failed. User with email {invite.Email} has already onboarded",
                            ResponseCode = "400"
                        };
                    }
                }
                
                // Get distinct applications and validate them upfront
                var distinctApps = invites.Select(i => i.Application).Distinct().ToList();
                var companyId = company.Id;
                var profileCount = await Db.UserProfiles.CountAsync();
                
                // Validate all applications once instead of in a loop
                foreach (var app in distinctApps)
                {
                    if (!Enum.TryParse(app, out Applications application))
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"Invalid application - {app.ToUpper()}",
                        };
                    }
                    
                    // Check subscription
                    var subscription = await Dbo.Subscriptions
                        .Include(x => x.PricingPlan)
                        .FirstOrDefaultAsync(x => x.TenantId == companyId && x.Application == application);
                        
                    if (subscription == null && profileCount >= _noOfFreeUsers)
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = $"You have not subscribed to {app.ToUpper()} application. You have reached the maximum number of free user.",
                        };
                    }
                    
                    if (subscription != null && (subscription.Status != "Successful" || 
                                                subscription.ExpiresOn <= DateTime.UtcNow))
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = "You currently do not have an active subscription",
                        };
                    }
                    
                    // Get count of existing permissions and invites for this app once
                    var usersWithPermission = await Db.AppPermissions
                        .CountAsync(x => x.Application == application.ToString() && x.SubscriptionStatus == SubscriptionStatus.Active.ToString() && x.IsEnabled);
                        
                    var numberOfPendingInvites = await Dbo.CompanyUserInvites
                        .CountAsync(x => x.TenantId == companyId.ToString() && 
                                    x.Application == application && 
                                    x.Status == "pending-finalization");
                    
                    var countOfInvitesForApp = invites.Count(i => i.Application == app);
                    
                    // Commented out subscription validation can be re-enabled here if needed
                    // Keeping same logic but making it more efficient
                    if (subscription != null)
                    {
                        if (subscription.SubscriptionFor <= (numberOfPendingInvites + countOfInvitesForApp + usersWithPermission))
                        {
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = $"You have reached the limit for {app.ToUpper()} invitations., you only have {subscription.SubscriptionFor - (numberOfPendingInvites + usersWithPermission)} invites left. Please upgrade your plan.",
                            };
                        }
                    }
                }
                
                var tenant = await _tenantService.GetTenantBySubdomain(subdomain);
                var tenantId = tenant.Id;
                
                // Pre-check all invitations at once
                var emailsToCheck = invites.Select(i => i.Email.ToLower()).ToList();
                var existingInvitations = await Dbo.CompanyUserInvites
                    .Where(i => emailsToCheck.Contains(i.Email.ToLower()))
                    .ToDictionaryAsync(i => i.Email.ToLower(), i => i);
                
                var personalEmails = new List<string>();
                var emailsWithWrongApp = new List<string>();
                var successfulInvites = 0;
                
                // Process each invite
                foreach (var invite in invites)
                {
                    var parseResult = Enum.TryParse(invite.Application, out Applications application);
                    if (!parseResult)
                    {
                        emailsWithWrongApp.Add(invite.Email);
                        continue;
                    }
                    
                    // Check existing invitations efficiently
                    if (existingInvitations.TryGetValue(invite.Email.ToLower(), out var existingInvitation))
                    {
                        DateTime twentyFourHoursLater = existingInvitation.LastUpdate.AddHours(24);
                        
                        if (existingInvitation.TenantId != tenantId.ToString() && 
                            existingInvitation.Status != "Revoked" && 
                            DateTime.UtcNow > twentyFourHoursLater)
                        {
                            // Skip silently per original logic
                            continue;
                        }
                        
                        // Check if invitation is still pending and valid
                        if (twentyFourHoursLater >= DateTime.UtcNow && 
                            !resendInvite && 
                            existingInvitation.Status == "pending-finalization")
                        {
                            return new GenericResponse
                            {
                                ResponseCode = "400",
                                ResponseMessage = $"This email - {invite.Email} - has a pending invitation. You can also use the resend invite option to resend the invite",
                            };
                        }
                    }
                    
                    // Create or update invite
                    var inviteCreated = await _companyUserInvite.CreateOrUpdateInvite(
                        new CompanyUserInviteVM
                        {
                            Email = invite.Email,
                            Application = Applications.Joble
                        }, 
                        tenantId.ToString());
                        
                    if (!inviteCreated)
                    {
                        return new GenericResponse
                        {
                            ResponseCode = "400",
                            ResponseMessage = "An error occurred while sending the invite"
                        };
                    }
                    
                    // Prepare email content
                    var encodedEmail = HttpUtility.UrlEncode(invite.Email);
                    var inviteUrl = string.Format(Utility.Constants.INVITE_URL, 
                        subdomain, 
                        $"{invitee.FirstName} {invitee.LastName}",
                        tenantId, 
                        application.ToString(), 
                        encodedEmail, 
                        invite.RoleId);
                        
                    var link = string.Format(Utility.Constants.JOBLE_FE_URL, subdomain);
                    
                    // Set appropriate application URL
                    switch (application)
                    {
                        case Applications.Echo:
                            link = link.Replace("joble", "echo");
                            break;
                        case Applications.JobID:
                            link = link.Replace("joble", "jobid");
                            break;
                        case Applications.JobPays:
                            link = link.Replace("joble", "jobpays");
                            break;
                    }
                    
                    // Prepare email parameters
                    var parameters = new Dictionary<string, string>
                    {
                        { "{invitee}", invitee.FirstName },
                        { "{company}", tenant.CompanyName },
                        { "{link}", link },
                        { "{url}", inviteUrl },
                        { "aiName", "" },
                        { "aiImage", "" }
                    };
                    
                    // Send email
                    var template = UpdateTemplateWithParams("company_invite_template", _environment, parameters);
                    var subject = "Company Invitation";
                    await _emailService.SendEmail(template, invite.Email, subject);
                    
                    successfulInvites++;
                }
                
                // Return success response
                return new GenericResponse
                {
                    ResponseCode = "200",
                    ResponseMessage = personalEmails.Any() 
                        ? $"Invitation sent successfully. Invitations was not sent to this email: {personalEmails[0]} as personal emails are not allowed" 
                        : "Invitation sent successfully",
                    Data = new 
                    {
                        PersonalEmails = personalEmails,
                        InvalidApps = emailsWithWrongApp,
                        SuccessfulInvites = successfulInvites
                    }
                };
            }
            catch (Exception ex)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = $"Error processing invitations: {ex.Message}",
                    Data = ex.StackTrace
                };
            }
        }
        #endregion

        #region Get AI Details
        public async Task<(string, string)> GetAiDetails(string token)
        {
            return (Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
            var headers = new Dictionary<string, string>()
            {
                { "Authorization", token }
            };

            var aiCharacter = await _apiCallService.MakeApiCallGenericAsync<AICharacterDto, AICharacterDto>(Utility.Constants.AI_DETAILS_BASEURL, "", Method.Get, null, headers);
            if (aiCharacter != null && aiCharacter.success)
            {
                return (aiCharacter?.data.name ?? Utility.Constants.AI_DEFAULT_NAME, aiCharacter?.data.image_url ?? Utility.Constants.AI_DEFAULT_IMAGE);
            }
            else
                return (Utility.Constants.AI_DEFAULT_NAME, Utility.Constants.AI_DEFAULT_IMAGE);
        }
        #endregion

        #region Revoke Invite
        public async Task<GenericResponse> RevokeInvite(string invitationId)
        {
            var inviteToRevoke = await Dbo.CompanyUserInvites
                .FirstOrDefaultAsync(i => i.Id == invitationId);

            if (inviteToRevoke == null)
            {
                return new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Invite not found"
                };
            }

            inviteToRevoke.Status = "Revoked";
            Dbo.CompanyUserInvites.Update(inviteToRevoke);
            await Dbo.SaveChangesAsync();

            return new GenericResponse
            {
                ResponseCode = "200",
                ResponseMessage = "Invite revoked successfully"
            };
        }
        #endregion
    }
}