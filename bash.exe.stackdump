Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFFA640) msys-2.0.dll+0x1FE8E
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210286019, 0007FFFFB5F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFB740  000210068E24 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFBA20  00021006A225 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC35360000 ntdll.dll
7FFC349F0000 KERNEL32.DLL
7FFC32B00000 KERNELBASE.dll
7FFC33DD0000 USER32.dll
7FFC32E80000 win32u.dll
7FFC34000000 GDI32.dll
7FFC33110000 gdi32full.dll
7FFC32880000 msvcp_win.dll
7FFC32920000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC34940000 advapi32.dll
7FFC34F00000 msvcrt.dll
7FFC34FB0000 sechost.dll
7FFC34810000 RPCRT4.dll
7FFC31F60000 CRYPTBASE.DLL
7FFC32F20000 bcryptPrimitives.dll
7FFC352E0000 IMM32.DLL
