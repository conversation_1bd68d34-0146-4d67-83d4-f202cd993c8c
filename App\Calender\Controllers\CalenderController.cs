using Google.Apis.Util;
using Jobid.App.ActivityLog.ViewModel;
using Jobid.App.Calender.Models;
using Jobid.App.Calender.ViewModel;
using Jobid.App.Helpers;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Contract;
using Jobid.App.Helpers.Enums;
using Jobid.App.Helpers.Exceptions;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Filters;
using Jobid.App.Helpers.Utils;
using Jobid.App.JobProject.ViewModel;
using Jobid.App.Tenant;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using static Jobid.App.JobProject.Enums.Enums;

namespace Jobid.App.Calender.Controllers
{
    /// <summary>
    /// Calender Controller
    /// </summary>
    //[PackageSubscriptionAndPermissionAuthorize(Applications.Joble)]
    [Authorize]
    [Route("api/[controller]")]
    [ApiController]
    [EndpointTrackerFilter(Applications.Joble, ApplicationSection.Calendar)]
    public class CalenderController : ControllerBase
    {
        #region Constructors and Properties
        private string ClaimId => User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value;
        private string ClaimName => User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Name)?.Value;

        /// <summary>
        /// Current user
        /// </summary>
        protected Guid? CurrentUserId => ClaimId is null ? null : Guid.Parse(ClaimId);
        protected string CurrentUser => ClaimName is null ? null : ClaimName;
        private readonly IUnitofwork Services_Repo;
        private ILogger _logger = Log.ForContext<CalenderController>();
        private readonly ITenantSchema _tenantSchema;
        private readonly ILogService _logService;
        private readonly IUserServices _userServices;

        /// <summary>
        /// Calender Constructor
        /// </summary>
        /// <param name="unitofwork"></param>
        /// <param name="tenantSchema"></param>
        /// <param name="logService"></param>
        /// <param name="userServices"></param>
        public CalenderController(
            IUnitofwork unitofwork,
            ITenantSchema tenantSchema,
            ILogService logService,
            IUserServices userServices)
        {
            this.Services_Repo = unitofwork;
            _tenantSchema = tenantSchema;
            _logService = logService;
            _userServices = userServices;
        }
        #endregion

        #region Create Meeting
        /// <summary>
        /// Add Meeting To Calender
        /// </summary>
        /// <param name="calenderVm"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPost("AddCalender")]
        public async Task<IActionResult> AddMeetingToCalender(CalenderVm calenderVm)
        {
            _logger.Information("AddMeetingToCalender called", calenderVm);
            calenderVm.UserId = CurrentUserId.Value;
            calenderVm.ReoccuringDeleteOptions = calenderVm.ReoccuringDeleteOptions ?? ReoccuringDeleteOptions.All;

            try
            {
                int duration = Convert.ToInt32((calenderVm.EndTime - calenderVm.StartDate).TotalMinutes);
                if (duration <= 0)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting End time cannot be behind meeting start time.",
                        Data = null
                    });
                }

                calenderVm.Host = HttpContext.Request.Host.Value;
                calenderVm.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                calenderVm.Token = token;

                // Check if the user creating the meeting is added to the invited user's list
                if (calenderVm.InvitedUsers.Contains(calenderVm.UserId.ToString()))
                {
                    calenderVm.InvitedUsers.Remove(calenderVm.UserId.ToString());
                }

                var result = await Services_Repo.CalenderService.CreateMeeting(calenderVm);

                if (result != null)
                {
                    // Log activity
                    var description = $"{CurrentUser} created a Meeting [{result.Name}]";
                    var summary = $"Meeting Created";
                    await LogActivity(description, summary, result.Id.ToString());

                    return Ok(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Calender created.",
                        Data = result
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Calender failed to be created.",
                        Data = result
                    });
                }

            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "AddMeetingToCalender failed: RecordNotFoundException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Calender failed to be created, please try again" });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "AddMeetingToCalender failed: DirtyFormException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "AddMeetingToCalender failed: OperationFailedException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Something went wrong, please try again later" });
            }
            catch (FileUploadException ex)
            {
                _logger.Error(ex, "AddMeetingToCalender failed: FileUploadException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "AddMeetingToCalender failed: UnauthorizedAccessException", calenderVm);
                return Unauthorized(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "403", DevResponseMessage = ex.Message, ResponseMessage = ex.Message });
            }
        }
        #endregion

        #region AI - Create Meeting
        /// <summary>
        /// Add Meeting To Calender
        /// </summary>
        /// <param name="calenderVm"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost("AI_AddCalender")]
        public async Task<IActionResult> AddMeetingToCalenderAI(CalenderVm calenderVm)
        {
            _logger.Information("AddMeetingToCalenderAI called", calenderVm);

            try
            {
                // Check if userId is a valid Guid
                if (!Guid.TryParse(calenderVm.UserId.ToString(), out Guid userId))
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid userId. UserId is required",
                        Data = null
                    });
                }

                calenderVm.UserId = userId;
                calenderVm.CreatedByAI = true;


                int duration = Convert.ToInt32((calenderVm.EndTime - calenderVm.StartDate).TotalMinutes);
                if (duration <= 0)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting End time cannot be behind meeting start time.",
                        Data = null
                    });
                }

                calenderVm.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                // Check if the user creating the meeting is added to the invited user's list
                if (calenderVm.InvitedUsers.Contains(calenderVm.UserId.ToString()))
                {
                    calenderVm.InvitedUsers.Remove(calenderVm.UserId.ToString());
                }

                var result = await Services_Repo.CalenderService.CreateMeeting(calenderVm);

                if (result != null)
                {
                    // Log activity
                    var user = await _userServices.GetUserById(userId.ToString());
                    var description = $"Britney created a Meeting [{result.Name}] on behalf of {user.FirstName}";
                    var summary = $"Meeting Created";
                    await LogActivity(description, summary, result.Id.ToString(), calenderVm.UserId.ToString());

                    return Ok(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Calender created.",
                        Data = result
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Calender failed to be created.",
                        Data = result
                    });
                }

            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "AddMeetingToCalenderAI failed: RecordNotFoundException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Calender failed to be created, please try again" });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "AddMeetingToCalenderAI failed: DirtyFormException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "AddMeetingToCalenderAI failed: OperationFailedException", calenderVm);
                return BadRequest(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Something went wrong, please try again later" });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "AddMeetingToCalenderAI failed: UnauthorizedAccessException", calenderVm);
                return Unauthorized(new ApiResponse<CalenderMeeting> { Data = null, ResponseCode = "403", DevResponseMessage = ex.Message, ResponseMessage = ex.Message });
            }
        }
        #endregion

        #region Upload Document
        /// <summary>
        /// Upload Document
        /// </summary>
        /// <param name="asset"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPost("UploadDocument")]
        public async Task<IActionResult> UploadDocument([FromForm] UploadDocumentDto asset)
        {
            try
            {
                _logger.Information("UploadDocument called", asset);

                var result = await Services_Repo.CalenderService.UploadDocument(asset);
                var doc = result.Count > 1 ? "Documents" : "Document";
                return Ok(new ApiResponse<List<string>>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"{doc} uploaded successfully.",
                    Data = result
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "UploadDocument failed: OperationFailedException", asset);
                return BadRequest(new ApiResponse<string> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE });
            }
            catch (FileUploadException ex)
            {
                _logger.Error(ex, "UploadDocument failed: OperationFailedException", asset);
                return BadRequest(new ApiResponse<string> { Data = null, ResponseCode = "400", ResponseMessage = ex.Message });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "UploadDocument failed: OperationFailedException", asset);
                return BadRequest(new ApiResponse<string> { Data = null, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE });
            }
        }
        #endregion

        #region Get Meeeting Recording Url
        /// <summary>
        /// Upload Document
        /// </summary>
        /// <param name="subDomain"></param>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_meetings)]
        [AllowAnonymous]
        [HttpGet("meeting-recording-url/{subDomain}/{meetinId}")]
        public async Task<IActionResult> MeetingRecordingUrl(string subDomain, string meetingId)
        {
            try
            {
                _logger.Information("MeetingRecordingUrl called", subDomain, meetingId);

                var result = await Services_Repo.CalenderService.GetMeetingRecordingUrl(subDomain, meetingId);
                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Failed to get meeting recording link",
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Failed to get meeting recording link",
                });
            }
        }
        #endregion

        #region Get Uploaded document
        /// <summary>
        /// Get Uploaded document
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="subsequentMeetingId"></param>
        /// <returns></returns>
        [HttpGet]
        [CustomAuthorize(Permissions.can_create_meetings)]
        [Route("GetDocument")]
        public async Task<IActionResult> GetUploadedDocument(string meetingId, string subsequentMeetingId)
        {
            _logger.Information("GetUploadedDocument called", meetingId);
            var result = await Services_Repo.CalenderService.GetUploadedDocument(meetingId, subsequentMeetingId);
            return Ok(result);
        }
        #endregion

        #region Delete Uploaded document
        /// <summary>
        /// Delete Uploaded document
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpDelete]
        [CustomAuthorize(Permissions.can_create_meetings)]
        [Route("DeleteDocument")]
        public async Task<IActionResult> DeleteUploadedDocument([FromBody] DeleteDocumentDto model)
        {
            try
            {
                _logger.Information("DeleteUploadedDocument called", model.MeetingId, model.FileName);
                var result = await Services_Repo.CalenderService.DeleteUploadedDocument(model.MeetingId, model.FileName, model.SubsequentMeetingId, model.ReoccuringDeleteOptions);
                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "DeleteUploadedDocument failed: RecordNotFoundException", model.MeetingId);
                return BadRequest(new ApiResponse<string> { Data = null, ResponseCode = "400", ResponseMessage = Utility.Constants.PRODUCTION_EXCEPTION_MESSAGE });
            }
        }
        #endregion

        #region Propose New Date And Time For Meeting
        /// <summary>
        /// Propose New Date And Time For Meeting
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut]
        [Route("ProposeNewDateAndTimeForMeeting")]
        public async Task<IActionResult> ProposeNewDateAndTimeForMeeting([FromBody] ProposeNewDateAndTimeDto model)
        {
            try
            {
                _logger.Information($"Propose new date and time for meeting {model.MeetingId}");
                var subdomain = this._tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                var response = await Services_Repo.CalenderService.ProposeNewTimeForMeeting(model, subdomain);
                return Ok(response);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Failed to propose new date and time, please try again",
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Accept or Reject Proposed Date And Time
        /// <summary>
        /// Accept or Reject Proposed Date And Time
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut]
        [Route("AcceptOrRejectProposedDateAndTime")]
        public async Task<IActionResult> AcceptOrRejectProposedDateAndTime([FromBody] AcceptOrRejectNewProposedDateAndTimeDto model)
        {
            try
            {
                _logger.Information($"Accept or reject proposed date and time for meeting {model.MeetingId}");
                var host = HttpContext.Request.Host.Value;
                var subDomain = this._tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                model.UserId = CurrentUserId.ToString();
                model.Subdomain = subDomain;

                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                model.Token = token;
                var response = await Services_Repo.CalenderService.AcceptOrRejectNewProposedDateOrTime(model);
                return Ok(response);
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error($"Unauthorized access {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error($"Record not found {ex.Message}");
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    Data = null,
                    ResponseCode = "400",
                    ResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Process Meeting Response
        /// <summary>
        /// Process Meeting Response
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        //[CustomAuthorize(Permissions.can_create_meetings)]
        [AllowAnonymous]
        [HttpPost]
        [Route("ProcessMeetingResponse")]
        public async Task<IActionResult> ProcessMeetingResponse([FromBody] MeetingResponseVm model)
        {
            _logger.Information("ProcessMeetingResponse called", model);

            var result = await Services_Repo.CalenderService.ProcessMeetingResponse(model);
            return Ok(new ApiResponse<String>
            {
                Data = null,
                ResponseCode = "200",
                DevResponseMessage = null,
                ResponseMessage = "Your response has been sent successfully",
            });
        }
        #endregion

        #region Add Personal Schedule
        /// <summary>
        ///
        /// </summary>
        /// <param name="personalSchedule"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPost("AddPersonalSchedule")]
        public async Task<IActionResult> AddPersonalSchedule(List<PersonalScheduleDto> personalSchedule)
        {
            _logger.Information("AddPersonalSchedule called", personalSchedule);

            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse<List<PersonalSchedule>>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request.",
                        Data = null
                    });
                }

                var result = await Services_Repo.CalenderService.AddPersonalSchedule(personalSchedule);
                if (result != null)
                {
                    // Log activity
                    var description = $"{CurrentUser} created Personal Schedule";
                    var summary = $"Personal Schedule Created";
                    await LogActivity(description, summary, null);

                    return Ok(new ApiResponse<List<PersonalSchedule>>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Personal Schedule created.",
                        Data = result
                    });

                }
                else
                {
                    return BadRequest(new ApiResponse<List<PersonalSchedule>>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Schedule cannot be created.",
                        Data = result
                    });
                }
            }
            catch (ArgumentException ex)
            {
                _logger.Error(ex, "AddPersonalSchedule failed: ArgumentException", personalSchedule);
                return BadRequest(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "400",
                    Data = null,
                    ResponseMessage = ex.Message
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "AddPersonalSchedule failed: DirtyFormException", personalSchedule);
                return BadRequest(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "400",
                    Data = null,
                    ResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Update or Reschedule Event or Meeting
        /// <summary>
        /// Update or Reschedule Event or Meeting
        /// </summary>
        /// <param name="rescheduleCalenderDto"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut("UpdateOrRescheduleCalender")]
        public async Task<IActionResult> UpdateOrRescheduleCalender(UpdateCalenderDto rescheduleCalenderDto)
        {
            _logger.Information("UpdateOrRescheduleCalender called", rescheduleCalenderDto);

            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request.",
                        Data = null
                    });
                }


                int duration = Convert.ToInt32((rescheduleCalenderDto.EndTime - rescheduleCalenderDto.StartDate).TotalMinutes);
                if (duration <= 0)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting End time cannot be behind meeting start time.",
                        Data = null
                    });
                }

                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                rescheduleCalenderDto.SubDomain = subdomain;
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                rescheduleCalenderDto.Token = token;
                var result = await Services_Repo.CalenderService.UpdateMeetingOrEvents(rescheduleCalenderDto);
                if (result != null)
                {
                    // Log activity
                    var description = $"{CurrentUser} updated a Meeting [{result.Name}]";
                    var summary = $"Meeting Updated";
                    await LogActivity(description, summary, result.Id.ToString());

                    return Ok(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Calender updated.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Calender cannot be updated.",
                        Data = result
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleCalender failed: InvalidOperationException", rescheduleCalenderDto);
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleCalender failed: RecordNotFoundException", rescheduleCalenderDto);
                return NotFound(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    Data = null,
                    ResponseMessage = "Something went wrong, please try again later."
                });
            }
        }
        #endregion

        #region Update or Reschedule Subsequent Meeting
        /// <summary>
        /// Update or Reschedule Subsequent Meeting
        /// </summary>
        /// <param name="updateSubsequentMeetingDto"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut("UpdateOrRescheduleSubsequentMeeting")]
        public async Task<IActionResult> UpdateOrRescheduleSubsequentMeeting(UpdateSubsequentMeetingDto updateSubsequentMeetingDto)
        {
            _logger.Information("UpdateOrRescheduleSubsequentMeeting called", updateSubsequentMeetingDto);

            try
            {
                int duration = Convert.ToInt32((updateSubsequentMeetingDto.EndTime - updateSubsequentMeetingDto.StartDate).TotalMinutes);
                if (duration <= 0)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting End time cannot be behind meeting start time.",
                        Data = null
                    });
                }

                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                updateSubsequentMeetingDto.SubDomain = subdomain;
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                updateSubsequentMeetingDto.Token = token;
                var result = await Services_Repo.CalenderService.UpdateSubsequentMeeting(updateSubsequentMeetingDto);
                if (result != null)
                {
                    // Log activity
                    var description = $"{CurrentUser} updated a Meeting [{result.Name}]";
                    var summary = $"Meeting Updated";
                    await LogActivity(description, summary, result.Id.ToString());

                    return Ok(new ApiResponse<SubsequentMeeting>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Meeting updated.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<SubsequentMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting cannot be updated.",
                        Data = result
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleSubsequentMeeting failed: InvalidOperationException", updateSubsequentMeetingDto);
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleSubsequentMeeting failed: RecordNotFoundException", updateSubsequentMeetingDto);
                return NotFound(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    Data = null,
                    ResponseMessage = "Something went wrong, please try again later."
                });
            }
        }
        #endregion

        #region AI - Update or Reschedule Event or Meeting
        /// <summary>
        /// Update or Reschedule Event or Meeting
        /// </summary>
        /// <param name="rescheduleCalenderDto"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPut("AI_UpdateOrRescheduleCalender")]
        public async Task<IActionResult> UpdateOrRescheduleCalenderAI(UpdateCalenderDto rescheduleCalenderDto)
        {
            _logger.Information("UpdateOrRescheduleCalenderAI called", rescheduleCalenderDto);

            // Check if userId is a valid Guid
            if (string.IsNullOrEmpty(rescheduleCalenderDto.UserId))
            {
                return BadRequest(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = "UserId is required",
                    Data = null
                });
            }

            rescheduleCalenderDto.AIReschedule = true;

            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Invalid request.",
                        Data = null
                    });
                }

                // Check if the meeting date is in the past
                if (rescheduleCalenderDto.StartDate < DateTime.UtcNow)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting date cannot be in the past.",
                        Data = null
                    });
                }

                int duration = Convert.ToInt32((rescheduleCalenderDto.EndTime - rescheduleCalenderDto.StartDate).TotalMinutes);
                if (duration <= 0)
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting End time cannot be behind meeting start time.",
                        Data = null
                    });
                }

                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                rescheduleCalenderDto.SubDomain = subdomain;
                var result = await Services_Repo.CalenderService.UpdateMeetingOrEvents(rescheduleCalenderDto);
                if (result != null)
                {
                    // Log activity
                    var user = await _userServices.GetUserById(rescheduleCalenderDto.UserId);
                    var description = $"Britney updated a Meeting [{result.Name}] on behalf of {user.FirstName}";
                    var summary = $"Meeting Updated";
                    await LogActivity(description, summary, result.Id.ToString());

                    return Ok(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Meeting updated.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<CalenderMeeting>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting update failed, please try again",
                        Data = result
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "403",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleCalenderAI failed: InvalidOperationException", rescheduleCalenderDto);
                return Unauthorized(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "UpdateOrRescheduleCalenderAI failed: RecordNotFoundException", rescheduleCalenderDto);
                return NotFound(new ApiResponse<CalenderMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    Data = null,
                    ResponseMessage = "Something went wrong, please try again later."
                });
            }
        }
        #endregion

        #region Update Personal Schedule
        /// <summary>
        /// Update personal schedule
        /// </summary>
        /// <param name="personalSchedule"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut("Personal/UpdateSchedule")]
        public async Task<IActionResult> UpdatePersonalSchedule(List<PersonalScheduleDto> personalSchedule)
        {
            _logger.Information("UpdatePersonalSchedule called", personalSchedule);

            try
            {
                if (ModelState.IsValid)
                {
                    var result = await Services_Repo.CalenderService.UpdatePersonalSchedule(personalSchedule);
                    if (!result)
                    {
                        return BadRequest(new ApiResponse<string>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "Schedules could not be updated.",
                            Data = "Failed"
                        });
                    }
                    else
                    {
                        // Log activity
                        var description = $"{CurrentUser} updated his/her Personal Schedule";
                        var summary = $"Personal Schedule Updated";
                        await LogActivity(description, summary, null);

                        return Ok(new ApiResponse<string>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "Schedules updated successfully.",
                            Data = "Success"
                        });
                    }
                }

                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Update Failed.",
                    Data = "Failed"
                });
            }
            catch (ArgumentException ex)
            {
                _logger.Error(ex, "UpdatePersonalSchedule failed: ArgumentException", personalSchedule);
                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    Data = null,
                    ResponseMessage = "Something went wrong, please try again later."
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "UpdatePersonalSchedule failed: DirtyFormException", personalSchedule);
                return BadRequest(new ApiResponse<string>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    Data = null,
                    ResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Delete/Cancel A meeting
        /// <summary>
        /// Delete/Cancel a meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPatch]
        [Route("DeleteMeeting/{meetingId}")]
        public async Task<IActionResult> CancelMeeting(string meetingId, [FromBody] CancelMeetingVm model)
        {
            _logger.Information("CancelMeeting called", meetingId);

            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                model.Token = token;
                var result = await Services_Repo.CalenderService.CancelMeeting(meetingId, model, subdomain);
                if (result)
                {
                    // Log activity
                    var description = $"{CurrentUser} deleted a Meeting";
                    var summary = $"Meeting Deleted";
                    await LogActivity(description, summary, meetingId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Meeting canceled successfully.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Meeting could not be canceled.",
                        Data = result
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "CancelMeeting failed: UnauthorizedAccessException", meetingId);
                return Unauthorized(new ApiResponse<bool>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "CancelMeeting failed: InvalidOperationException", meetingId);
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "CancelMeeting failed: RecordNotFoundException", meetingId);
                return NotFound(new ApiResponse<bool>
                {
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Meeting By UserId
        /// <summary>
        /// Gets a user's meeting shedules
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("get_user_meetings")]
        public async Task<IActionResult> GetMeetingByUserId([FromQuery] GetUserScheduleVm model)
        {
            _logger.Information("GetMeetingByUserId called", model);
            var result = await Services_Repo.CalenderService.GetCalenderById(model);
            if (result != null)
            {

                // Log activity
                //var description = $"Meetings by userId retrieved by {CurrentUser}";
                //var summary = $"Meetings for a user retrieved";
                //await LogActivity(description, summary, null);

                return Ok(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "User calender meetings retrieved sucessfully.",
                    Data = result
                });

            }
            else
            {
                _logger.Error("GetMeetingByUserId failed: RecordNotFoundException", model);
                return NotFound(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Calender meetings could be retrieved.",
                    Data = result
                });
            }
        }
        #endregion

        #region Search User Calender Meeting
        /// <summary>
        /// Gets a user's meeting shedules
        /// </summary>
        /// <param name="searchUserCalender"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("search_user_calender")]
        public async Task<IActionResult> SearchUserCalenderToGetMeeting([FromQuery] SearchUserCalenderToGetMeetingDto searchUserCalender)
        {
            _logger.Information("SearchUserCalenderToGetMeeting called", searchUserCalender);
            var result = await Services_Repo.CalenderService.SearchUserCalenderToGetMeeting(searchUserCalender);
            if (result != null)
            {
                // Log activity
                var description = $"{CurrentUser} searched for meetings";
                var summary = $"Meeting Search";
                await LogActivity(description, summary, null);

                return Ok(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "User calender meetings retrieved sucessfully.",
                    Data = result
                });

            }
            else
            {
                _logger.Error("SearchUserCalenderToGetMeeting failed: RecordNotFoundException", searchUserCalender);
                return NotFound(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Calender meetings could be retrieved.",
                    Data = result
                });
            }
        }
        #endregion

        #region To display the most recent meeting on the Dashboard By UserId
        /// <summary>
        /// Gets a user's meeting shedules
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("get_user_most_recent_meetings")]
        public async Task<IActionResult> GetMostRecentMeetingByUserId([FromQuery] GetUserScheduleVm model)
        {
            _logger.Information("GetMostRecentMeetingByUserId called", model);
            var result = await Services_Repo.CalenderService.GetMostRecentCalenderById(model);
            if (result != null)
            {
                // Log activity
                //var description = $"Meetings by userId retrieved by {CurrentUser}";
                //var summary = $"Meetings for a user retrieved";
                //await LogActivity(description, summary, null);

                return Ok(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "User calender meetings retrieved sucessfully.",
                    Data = result
                });

            }
            else
            {
                _logger.Error("GetMostRecentMeetingByUserId failed: RecordNotFoundException", model);
                return NotFound(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "Calender meetings could be retrieved.",
                    Data = result
                });
            }
        }
        #endregion

        #region Get Meeting By MeetingId
        /// <summary>
        /// Get Meeting By MeetingId
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet]
        [Route("GetMeetingById/{meetingId}")]
        public async Task<IActionResult> GetMeetingById(string meetingId)
        {
            _logger.Information("GetMeetingById called", meetingId);
            var result = await Services_Repo.CalenderService.GetMeetingById(meetingId);

            return Ok(new ApiResponse<CalenderMeeting>
            {
                ResponseCode = "200",
                ResponseMessage = "User calender meetings retrieved sucessfully.",
                Data = result
            });
        }
        #endregion

        #region Get Meeting By RTC MeetingId
        /// <summary>
        /// Get Meeting By RTC MeetingId
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="basicDetails"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetMeetingByRTCId/{meetingId}")]
        public async Task<IActionResult> GetMeetingByRTCId(string meetingId, bool basicDetails = true)
        {
            _logger.Information("GetMeetingByRTCId called", meetingId);
            var result = await Services_Repo.CalenderService.GetMeetingByRTCId(meetingId, basicDetails);
            return Ok(result);
        }
        #endregion

        #region Get Last Held Reccuring Meeting By RTC MeetingId
        /// <summary>
        /// Get last held reccuring meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetLastHeldReccuringMeeting/{meetingId}")]
        public async Task<IActionResult> GetLastHeldMeetingForReccuringMeeting(string meetingId)
        {
            _logger.Information("GetMeetingByRTCId called", meetingId);
            var result = await Services_Repo.CalenderService.GetAILastHeldMeetingForReccuringMeeting(meetingId);
            return Ok(result);
        }
        #endregion

        #region Add or Update Meeting Note
        /// <summary>
        /// Add or Update Meeting Note
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost]
        [Route("AddOrUpdateMeetingNote")]
        public async Task<IActionResult> AddOrUpdateMeetingNote([FromBody] AddMeetingNotesDto model)
        {
            try
            {
                _logger.Information("AddOrUpdateMeetingNote called", model);
                var result = await Services_Repo.CalenderService.AddOrUpdateMeetingNotes(model);
                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new GenericResponse
                {
                    ResponseCode = "400",
                    ResponseMessage = "Something went wrong, please try again later",
                    DevResponseMessage = ex.Message
                });
            }
        }
        #endregion

        #region Get meeting notes
        /// <summary>
        /// Get meeting notes
        /// </summary>
        /// <param name="retMeetingId"></param>
        /// <param name="userId"></param>
        /// <param name="subSequentMeetingId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetMeetingNotes")]
        public async Task<IActionResult> GetMeetingNotes([FromQuery] string retMeetingId, [FromQuery] string userId, [FromQuery] Guid? subSequentMeetingId)
        {
            _logger.Information("GetMeetingNotes called", retMeetingId);
            var result = await Services_Repo.CalenderService.GetMeetingNotes(retMeetingId, userId, subSequentMeetingId);
            return Ok(result);
        }
        #endregion

        #region Download Meeting Notes As PDF
        /// <summary>
        /// Download Meeting Notes As PDF
        /// </summary>
        /// <param name="meetingNoteId"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("DownloadMeetingNotesAsPDF/{meetingNoteId}")]
        public async Task<IActionResult> DownloadMeetingNotesAsPDF([FromRoute] string meetingNoteId)
        {
            _logger.Information("DownloadMeetingNotesAsPDF called", meetingNoteId);
            var result = await Services_Repo.CalenderService.DownloadMeetingNotesAsPDF(meetingNoteId);
            var pdf = result.Data as byte[];
            var res = File(pdf, "application/pdf", "download.pdf");
            result.Data = res;

            return File(pdf, "application/pdf", "download.pdf");
            return Ok(result);
        }
        #endregion

        #region Send Meeting Notes As An Email
        /// <summary>
        /// Send Meeting Notes As An Email
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SendMeetingNotesAsEmail")]
        public async Task<IActionResult> SendMeetingNotesAsEmail([FromBody] SendMeetingNotesDto model)
        {
            _logger.Information("SendMeetingNotesAsEmail called", model);
            var result = await Services_Repo.CalenderService.SendMeetingNotesAsEmail(model);
            return Ok(result);
        }
        #endregion

        #region Delete Meeting Notes
        /// <summary>
        /// Deletes meeting Note
        /// </summary>
        /// <param name="meetingNoteId"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpDelete]
        [Route("DeleteMeetingNote/{meetingNoteId}")]
        public async Task<IActionResult> DeleteMeetingNotes(Guid meetingNoteId, CancellationToken cancellationToken)
        {
            _logger.Information("DeleteMeetingNotes called", meetingNoteId);
            var result = await Services_Repo.CalenderService.DeleteMeetingNotes(meetingNoteId, cancellationToken);
            return Ok(result);
        }
        #endregion

        #region Convert HTML to PDF
        /// <summary>
        /// Converts HTML content to PDF document
        /// </summary>
        /// <param name="model">HTML content and optional filename</param>
        /// <returns>PDF document as file download</returns>
        [HttpPost]
        [Route("ConvertHtmlToPdf")]
        public async Task<IActionResult> ConvertHtmlToPdf([FromBody] ViewModel.HtmlToPdfViewModel model)
        {
            _logger.Information("ConvertHtmlToPdf called", model);
            var response = await Services_Repo.CalenderService.ConvertHtmlToPdf(model);

            if (response.ResponseCode != "200")
            {
                // Return appropriate status code based on the response code
                if (response.ResponseCode == "500")
                {
                    _logger.Error("PDF generation failed with server error", response.DevResponseMessage);
                    return StatusCode(500, response);
                }
                else
                {
                    _logger.Warning("PDF generation failed with client error", response.DevResponseMessage);
                    return BadRequest(response);
                }
            }

            try
            {
                // Extract file information from the response data
                dynamic fileData = response.Data;
                string fileName = fileData.FileName;
                string contentType = fileData.ContentType;
                byte[] fileContents = fileData.FileContents;

                // Return the PDF as a file download
                return File(fileContents, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.Error("Error processing PDF response data", ex);
                return StatusCode(500, new { ResponseCode = "500", ResponseMessage = "Error processing PDF data", DevResponseMessage = ex.Message });
            }
        }
        #endregion

        #region Get users availabilities
        /// <summary>
        /// Get users availabilities
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [ServiceFilter(typeof(ApiKeyAttribute))]
        [HttpPost]
        [Route("GetUsersAvailabilities")]
        public async Task<IActionResult> GetUsersAvailabilities(GetUserAvailabilityDto model)
        {
            _logger.Information("GetUsersAvailabilities called", model);
            var result = await Services_Repo.CalenderService.GetUsersAvailability(model);
            return Ok(result);
        }
        #endregion

        #region To display the most recent meeting on the Dashboard By MeetingId
        /// <summary>
        /// Get Meeting By MeetingId
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet]
        [Route("GetMostRecentMeetingById/{meetingId}")]
        public async Task<IActionResult> GetMostRecentMeetingById(string meetingId)
        {
            _logger.Information("GetMostRecentMeetingById called", meetingId);
            var result = await Services_Repo.CalenderService.GetMostRecentMeetingById(meetingId);

            return Ok(new ApiResponse<CalenderMeeting>
            {
                ResponseCode = "200",
                ResponseMessage = "User calender meetings retrieved sucessfully.",
                Data = result
            });
        }
        #endregion

        #region To display the most recent meeting on the Dashboard By MeetingId For AI
        /// <summary>
        /// Get Meeting By MeetingId
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("AIGetMostRecentMeetingById/{meetingId}")]
        public async Task<IActionResult> AIGetMostRecentMeetingById(string meetingId)
        {
            _logger.Information("AIGetMostRecentMeetingById called", meetingId);
            var result = await Services_Repo.CalenderService.GetMostRecentMeetingById(meetingId);

            return Ok(new ApiResponse<CalenderMeeting>
            {
                ResponseCode = "200",
                ResponseMessage = "User calender meetings retrieved sucessfully.",
                Data = result
            });
        }
        #endregion

        #region Get Meetings for group of users
        /// <summary>
        /// Gets the meeting for all the users in a tenant with any userId
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPost]
        [Route("GetAllMeetings")]
        public async Task<IActionResult> GetAllMeetings([FromBody] GetUsersMeetingVm model)
        {
            _logger.Information("GetAllMeetings called", model);
            var result = await Services_Repo.CalenderService.GetAllMeetings(model);

            return Ok(result);
        }
        #endregion

        #region Get Events By userId
        /// <summary>
        /// Get User's calender events
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("get-user-events")]
        public async Task<IActionResult> GetEventsByUserId([FromQuery] GetUserScheduleVm model)
        {
            _logger.Information("GetEventsByUserId called", model);
            var result = await Services_Repo.CalenderService.GetEventsByUserId(model);
            if (result != null)
            {
                return Ok(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = $"Events found for the user {model.UserId}.",
                    Data = result
                });

            }
            else
            {
                return BadRequest(new ApiResponse<List<CalenderMeeting>>
                {
                    ResponseCode = "400",
                    ResponseMessage = "Calender events could not be retrieved.",
                    Data = result
                });
            }
        }
        #endregion

        #region Remove Member From Calender Schedule
        /// <summary>
        /// Remove Member From Calender Schedule
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="calenderId"></param>
        /// <param name="externalTeamMemberEmail"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpDelete("RemoveMemberFromCalender")]
        public async Task<IActionResult> RemoveMemberFromCalenderSchedule([FromQuery] string userId, [FromQuery] string calenderId, [FromQuery] string externalTeamMemberEmail)
        {
            try
            {
                _logger.Information("RemoveMemberFromCalenderSchedule called", userId, calenderId);
                var result = await Services_Repo.CalenderService.RemoveMemeberFromCalenderByUserId(userId, calenderId, CurrentUserId.ToString(), externalTeamMemberEmail);
                if (result)
                {
                    // Log activity
                    var description = $"{CurrentUser} removed a user from a Meeting";
                    var summary = $"User Removed From Meeting";
                    await LogActivity(description, summary, calenderId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Calender deleted.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Calender cannot be deleted.",
                        Data = result
                    });
                }

            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "RemoveMemberFromCalenderSchedule failed: UnauthorizedAccessException", userId, calenderId);
                return Unauthorized(new ApiResponse<bool>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "RemoveMemberFromCalenderSchedule failed: OperationFailedException", userId, calenderId);
                return Unauthorized(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "RemoveMemberFromCalenderSchedule failed: RecordNotFoundException", userId, calenderId);
                return NotFound(new ApiResponse<bool>
                {
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Add Member To Calender Schedule
        /// <summary>
        /// Remove Member From Calender Schedule
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPatch("AddmemberToCalender")]
        public async Task<IActionResult> AddMemberToCalenderMeeting([FromBody] AddMemberToMeeting request)
        {
            try
            {
                _logger.Information("AddMemberTocalender called", request);
                request.Subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var token = this.HttpContext.Request.Headers["Authorization"].ToString();
                request.Token = token;
                request.LoggedInUserId = CurrentUserId.ToString();
                var result = await Services_Repo.CalenderService.AddMemberToCalender(request);
                if (result)
                {
                    // Log activity
                    var description = $"{CurrentUser} added a user from a Meeting";
                    var summary = $"User added to Meeting";
                    await LogActivity(description, summary, request.CalenderId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "User added to Meeting",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "Calender cannot be deleted.",
                        Data = result
                    });
                }

            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "AddMemberToCalenderSchedule failed: UnauthorizedAccessException", request);
                return Unauthorized(new ApiResponse<bool>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "AddMemberToCalenderSchedule failed: UnauthorizedAccessException", request);
                return Unauthorized(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "AddMemberToCalenderSchedule failed: UnauthorizedAccessException", request);
                return NotFound(new ApiResponse<bool>
                {
                    ResponseCode = "404",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = false
                });
            }
        }
        #endregion

        #region Get Personal Schedule By UserId
        /// <summary>
        /// Gets personal schedule by user id
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("GetPersonalSchedule/{userId}")]
        public async Task<IActionResult> GetPersonalScheduleByUserId(string userId)
        {
            _logger.Information("GetPersonalScheduleByUserId called", userId);
            var result = await Services_Repo.CalenderService.GetPersonalScheduleByUserId(userId);
            if (result != null)
            {
                return Ok(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "User personal schedules retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                _logger.Error("GetPersonalScheduleByUserId failed: RecordNotFoundException", userId);
                return NotFound(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "400",
                    ResponseMessage = "No Personal schedules found",
                    Data = result
                });
            }
        }
        #endregion

        #region Get Personal Schedules By UserIds
        /// <summary>
        /// Get personal schedules by userIds
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        [HttpPost("GetPersonalSchedules")]
        public async Task<IActionResult> GetPersonalSchedules([FromBody] List<string> userIds)
        {
            _logger.Information("GetPersonalSchedules called", userIds);
            var result = await Services_Repo.CalenderService.GetPersonalSchedules(userIds);
            if (result != null)
            {
                return Ok(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Personal schedules retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                return NotFound(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "No Personal schedules found",
                    Data = result
                });
            }
        }
        #endregion

        #region Get Personal Schedules By UserIds
        /// <summary>
        /// Get personal schedules by userIds
        /// </summary>
        /// <param name="userIds"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPost("GetPersonalSchedulesForAi")]
        public async Task<IActionResult> GetPersonalSchedulesForAi([FromBody] List<string> userIds)
        {
            _logger.Information("GetPersonalSchedules called", userIds);
            var result = await Services_Repo.CalenderService.GetPersonalSchedules(userIds);
            if (result != null)
            {
                return Ok(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Personal schedules retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                return NotFound(new ApiResponse<List<PersonalSchedule>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "No Personal schedules found",
                    Data = result
                });
            }
        }
        #endregion

        #region Delete Personal Schedule
        /// <summary>
        /// Delete a user's custom personal schedule or all expired custom schedules
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpDelete("DeletePersonalSchedule")]
        public async Task<IActionResult> DeletePersonalSchedule([FromBody] DeletePersonalScheduleDto model)
        {
            try
            {
                _logger.Information("DeletePersonalSchedule called", model);

                // Set the current user ID
                model.UserId = CurrentUserId.ToString();

                var result = await Services_Repo.CalenderService.DeletePersonalSchedule(model);

                // Log activity
                if (model.DeleteExpiredSchedules)
                {
                    var description = $"{CurrentUser} deleted expired custom personal schedules";
                    var summary = $"Expired Personal Schedules Deleted";
                    await LogActivity(description, summary, null);
                }
                else
                {
                    var description = $"{CurrentUser} deleted a personal schedule";
                    var summary = $"Personal Schedule Deleted";
                    await LogActivity(description, summary, model.PersonalScheduleId);
                }

                return Ok(result);
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "DeletePersonalSchedule failed: DirtyFormException", model);
                return BadRequest(new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "DeletePersonalSchedule failed: RecordNotFoundException", model);
                return NotFound(new ApiResponse<object>
                {
                    ResponseCode = "404",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "DeletePersonalSchedule failed: UnauthorizedAccessException", model);
                return Unauthorized(new ApiResponse<object>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.Error(ex, "DeletePersonalSchedule failed: InvalidOperationException", model);
                return BadRequest(new ApiResponse<object>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "DeletePersonalSchedule failed: Exception", model);
                return BadRequest(new ApiResponse<object>
                {
                    ResponseCode = "500",
                    ResponseMessage = "An error occurred while deleting the personal schedule",
                    DevResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Create External Meeting
        /// <summary>
        /// Creates External Meeting
        /// </summary>
        /// <param name="externalMeetingDto"></param>
        /// <returns>ExternalMeeting</returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPost("CreateExternalMeeting")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> CreateExternalMeeting(ExternalMeetingDto externalMeetingDto)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    externalMeetingDto.UserId = CurrentUserId.ToString();
                    externalMeetingDto.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                    _logger.Information("CreateExternalMeeting called", externalMeetingDto);
                    var result = await Services_Repo.CalenderService.CreatExternalMeetingOrEvent(externalMeetingDto);
                    if (result != null)
                    {
                        // Log activity
                        var description = $"{CurrentUser} created an External Meeting [{result.MeetingName}]";
                        var summary = $"External Meeting Created";
                        await LogActivity(description, summary, result.Id.ToString());

                        return Ok(new ApiResponse<ExternalMeeting>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "External Meeting created successfully.",
                            Data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new ApiResponse<ExternalMeeting>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "External Meeting could not be created.",
                            Data = result
                        });
                    }
                }
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ModelState.ToString(),
                    Data = null
                });
            }
            catch (DirtyFormException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (OperationFailedException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = null
                });
            }
        }
        #endregion

        #region Update External Meeting
        /// <summary>
        /// Updates External Meeting
        /// </summary>
        /// <param name="externalMeetingId"></param>
        /// <param name="externalMeetingDto"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut]
        [Route("UpdateExternalMeeting/{externalMeetingId}")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> UpdateExternalMeeting(string externalMeetingId, ExternalMeetingDto externalMeetingDto)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    externalMeetingDto.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                    externalMeetingDto.UserId = CurrentUserId.ToString();

                    _logger.Information("UpdateExternalMeeting called", externalMeetingId, externalMeetingDto);
                    var result = await Services_Repo.CalenderService.UpdateExternalMeeting(externalMeetingId, externalMeetingDto);
                    if (result)
                    {
                        // Log activity
                        var description = $"{CurrentUser} updated an External Meeting";
                        var summary = $"External Meeting Updated";
                        await LogActivity(description, summary, externalMeetingId);

                        return Ok(new ApiResponse<bool>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "External Meeting updated successfully.",
                            Data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "External Meeting could not be updated.",
                            Data = result
                        });
                    }
                }
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ModelState.ToString(),
                    Data = false
                });
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.Error(ex, "UpdateExternalMeeting failed: UnauthorizedAccessException", externalMeetingId, externalMeetingDto);
                return Unauthorized(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex, "UpdateExternalMeeting failed: DirtyFormException", externalMeetingId, externalMeetingDto);
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (OperationFailedException ex)
            {
                _logger.Error(ex, "UpdateExternalMeeting failed: OperationFailedException", externalMeetingId, externalMeetingDto);
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    DevResponseMessage = "Rtc endpoint for updating a meeting failed",
                    Data = null
                });
            }
            catch (RecordAlreadyExistException ex)
            {
                _logger.Error(ex, "UpdateExternalMeeting failed: RecordAlreadyExistException", externalMeetingId, externalMeetingDto);
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = $"{ex.Message}",
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex, "UpdateExternalMeeting failed: RecordNotFoundException", externalMeetingId, externalMeetingDto);
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, please try again later",
                    Data = null
                });
            }
        }
        #endregion

        #region Get External Meeting By Id
        /// <summary>
        /// Get external meeting by Id
        /// </summary>
        /// <param name="Id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet]
        [Route("GetExternalMeetingById/{Id}")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> GetExternalMeetingById(string Id)
        {
            try
            {
                Log.Information($"Atempt to get external meeting for {Id}");

                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                var result = await Services_Repo.CalenderService.GetExternalMeetingById(Id, subdomain);

                // Log activity
                if (CurrentUser is not null)
                {
                    var description = $"External meeting retrieved by {CurrentUser}";
                    var summary = $"External meeting retrieved";
                    await LogActivity(description, summary, Id);
                }

                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                Log.Error($"Error occured while getting external meeting for {Id} {ex.Message}");
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "500",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, external meeting could not be retrived",
                    Data = null
                });
            }
        }
        #endregion

        #region Book External Meeting/Events
        /// <summary>
        /// Books External Meeting/Event
        /// </summary>
        /// <param name="model"></param>
        /// <returns>BookedExternalMeeting</returns>
        [AllowAnonymous]
        [HttpPost("BookExternalMeeting")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> BookExternalMeeting(BookExternalMeetingDto model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    model.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                    _logger.Information("BookExternalMeeting called", model);
                    var result = await Services_Repo.CalenderService.BookExternalMeetingorEvent(model);
                    if (result != null)
                    {
                        return Ok(new ApiResponse<BookedExternalMeeting>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "External Meeting booked successfully.",
                            Data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new ApiResponse<BookedExternalMeeting>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "External Meeting could not be booked.",
                            Data = result
                        });
                    }
                }
                return BadRequest(new ApiResponse<BookedExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ModelState.ToString(),
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, $"Error occured while booking external meeting by {CurrentUser}", nameof(BookExternalMeeting));
                return BadRequest(new ApiResponse<BookedExternalMeeting> { ResponseCode = "400", ResponseMessage = ex.Message, Data = null });
            }
            catch (OperationNotAllowedException ex)
            {
                _logger.Error(ex.Message, $"Error occured while booking external meeting by {CurrentUser}", nameof(BookExternalMeeting));
                return BadRequest(new ApiResponse<BookedExternalMeeting> { ResponseCode = "400", DevResponseMessage = ex.Message, Data = null, ResponseMessage = ex.Message });
            }
            catch (DirtyFormException ex)
            {
                _logger.Error(ex.Message, $"Error occured while booking external meeting by {CurrentUser}", nameof(BookExternalMeeting));
                return BadRequest(new ApiResponse<BookedExternalMeeting> { ResponseCode = "500", ResponseMessage = ex.Message, Data = null });
            }
        }
        #endregion

        #region Update Booked External Meeting - By Joble User
        [HttpPut]
        [Route("UpdateBookedExternalMeeting")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> UpdateBookedExternalMeeting(UpdateBookedExtMeetingDto model)
        {
            try
            {
                model.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);
                _logger.Information("UpdateBookedExternalMeeting called", model);
                var result = await Services_Repo.CalenderService.UpdateBookedMeeting(model);
                return result.ResponseCode == "200" ? Ok(result) : BadRequest(result);
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, $"Error occured while updating external meeting by {CurrentUser}", nameof(UpdateBookedExternalMeeting));
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, meeting could not be updated",
                    Data = null
                });
            }
            catch (OperationNotAllowedException ex)
            {
                _logger.Error(ex.Message, $"Error occured while updating external meeting by {CurrentUser}", nameof(UpdateBookedExternalMeeting));
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Lock or Unlock Selected Date For External Booking
        /// <summary>
        /// Lock or Unlock Selected Date For External Booking
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("LockOrUnlockSelectedDateForExternalBooking")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> LockOrUnlockSelectedDateForExternalBooking([FromBody] LockOrUnlockSelectedDateDto model)
        {
            _logger.Information("LockOrUnlockSelectedDateForExternalBooking called", model);
            var result = await Services_Repo.CalenderService.LockOrUnlockSelectedDate(model);
            return result.ResponseCode == "200" ? Ok(result) : BadRequest(result);
        }
        #endregion

        #region Reschedule Booked External Meeting
        /// <summary>
        /// Reschedule Booked External Meeting
        /// </summary>
        /// <param name="bookedExternalMeetingId"></param>
        /// <param name="model"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut]
        [Route("RescheduleBookedExternalMeeting/{bookedExternalMeetingId}")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> RescheduleBookedExternalMeeting(string bookedExternalMeetingId, BookExternalMeetingDto model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    model.SubDomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                    _logger.Information("RescheduleBookedExternalMeeting called", bookedExternalMeetingId, model);
                    var result = await Services_Repo.CalenderService.ReScheduleBookedMeeting(bookedExternalMeetingId, model);
                    if (result)
                    {
                        return Ok(new ApiResponse<bool>
                        {
                            ResponseCode = "200",
                            ResponseMessage = "External Meeting rescheduled successfully.",
                            Data = result
                        });
                    }
                    else
                    {
                        return BadRequest(new ApiResponse<bool>
                        {
                            ResponseCode = "400",
                            ResponseMessage = "External Meeting could not be rescheduled.",
                            Data = result
                        });
                    }
                }
                return BadRequest(new ApiResponse<bool>
                {
                    ResponseCode = "400",
                    ResponseMessage = ModelState.ToString(),
                    Data = false
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, $"Error occured while rescheduling external meeting by {CurrentUser}", nameof(RescheduleBookedExternalMeeting));
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, meeting could not be rescheduled",
                    Data = null
                });
            }
        }
        #endregion

        #region Cancel Booked External Meeting
        /// <summary>
        /// Cancel Booked External Meeting
        /// </summary>
        /// <param name="bookedExternalMeetingId"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpPut]
        [Route("CancelBookedExternalMeeting/{bookedExternalMeetingId}")]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(typeof(bool), 400)]
        public async Task<IActionResult> CancelBookedExternalMeeting(string bookedExternalMeetingId, string reason)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                _logger.Information("CancelBookedExternalMeeting called", bookedExternalMeetingId, reason);
                var result = await Services_Repo.CalenderService.CancelBookedExternalMeeting(bookedExternalMeetingId, reason, subdomain);
                if (result == "Success")
                {
                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "Your Meeting cancelled successfully.",
                        Data = true
                    });
                }
                else if (result == "Failure")
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "External Meeting could not be cancelled.",
                        Data = false
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = result,
                        Data = false
                    });
                }
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, $"Error occured while cancelling external meeting by {CurrentUser}", nameof(CancelBookedExternalMeeting));
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, meeting could not be cancelled",
                    Data = null
                });
            }
        }
        #endregion

        #region Cancel External Meeting
        /// <summary>
        /// Cancel External Meeting by Id
        /// </summary>
        /// <param name="externalMeetingId"></param>
        /// <param name="reason"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut]
        [Route("CancelExternalMeeting/{externalMeetingId}")]
        [ProducesResponseType(typeof(bool), 200)]
        [ProducesResponseType(typeof(bool), 400)]
        public async Task<IActionResult> CancelExternalMeeting(string externalMeetingId, string reason)
        {
            try
            {
                var subdomain = _tenantSchema.ExtractSubdomainFromRequest(HttpContext);

                _logger.Information("CancelExternalMeeting called", externalMeetingId, reason);
                var result = await Services_Repo.CalenderService.CancelExternalMeetingOrEvent(externalMeetingId, subdomain);
                if (result)
                {
                    // Log activity
                    var description = $"{CurrentUser} cancelled an External Meeting";
                    var summary = $"External Meeting Cancelled";
                    await LogActivity(description, summary, externalMeetingId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "External Meeting cancelled successfully.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "External Meeting could not be cancelled.",
                        Data = result
                    });
                }
            }
            catch (OperationFailedException ex)
            {
                return BadRequest(new ApiResponse<bool>
                {
                    Data = false,
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, meeting could not be cancelled"
                });
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<bool> { Data = false, ResponseCode = "400", DevResponseMessage = ex.Message, ResponseMessage = "Something went wrong, try again later" });
            }
        }
        #endregion

        #region Get External Meetings of a user
        /// <summary>
        /// Get External Meetings of a user
        /// </summary>
        /// <param name="userId"></param>
        /// <returns>ExternalMeeting</returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("GetExternalMeetings/{userId}")]
        public async Task<IActionResult> GetExternalMeetings(string userId)
        {
            try
            {
                _logger.Information("GetExternalMeetings called", userId);
                var result = await Services_Repo.CalenderService.GetExternalMeetingOrEventByUserId(userId);

                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<GenericResponse>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Get Booked External Meetings using externalmeetingId
        /// <summary>
        /// Get Booked External Meeting using externalmeetingId
        /// </summary>
        /// <param name="meetingId"></param>
        /// <returns>BookedExternalMeeting</returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("GetBookedExternalMeeting/{meetingId}")]
        public async Task<IActionResult> GetBookedExternalMeetings(string meetingId)
        {
            _logger.Information("GetBookedExternalMeetings called", meetingId);
            var result = await Services_Repo.CalenderService.GetBookedExternalMeetingOrEventByMeetingId(meetingId);
            if (result != null)
            {
                //// Log activity
                //var description = $"Retrieved booked external meeting with id {meetingId}";
                //var summary = $"Booked external meeting retrieved";
                //await LogActivity(description, summary, meetingId);

                return Ok(new ApiResponse<List<BookedExternalMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Booked External Meetings retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                return NotFound(new ApiResponse<List<BookedExternalMeeting>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "No Booked External Meetings found.",
                    Data = result
                });
            }
        }
        #endregion

        #region Get Booked External Meetings by userId
        /// <summary>
        /// Get Booked External Meetings by userId
        /// </summary>
        /// <param name="userId"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("GetBookedExternalMeetingsByUserId/{userId}")]
        public async Task<IActionResult> GetBookedExternalMeetingsByUserId(string userId)
        {
            var result = await Services_Repo.CalenderService.GetBookedExternalMeetingByUserId(userId);
            if (result != null)
            {
                return Ok(new ApiResponse<List<BookedExternalMeeting>>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Booked External Meetings retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                return NotFound(new ApiResponse<List<BookedExternalMeeting>>
                {
                    ResponseCode = "404",
                    ResponseMessage = "No Booked External Meetings found.",
                    Data = result
                });
            }
        }
        #endregion

        #region Get booked external meeting by id
        /// <summary>
        /// Get booked external meeting by id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [AllowAnonymous]
        [HttpGet("GetBookedExternalMeetingById/{id}")]
        public async Task<IActionResult> GetBookedExternalMeetingById(string id)
        {
            var result = await Services_Repo.CalenderService.GetBookedExternalMeetingById(id);
            if (result != null)
            {
                return Ok(new ApiResponse<BookedExternalMeeting>
                {
                    ResponseCode = "200",
                    ResponseMessage = "Booked External Meeting retrieved sucessfully.",
                    Data = result
                });
            }
            else
            {
                return NotFound(new ApiResponse<BookedExternalMeeting>
                {
                    ResponseCode = "404",
                    ResponseMessage = "No Booked External Meeting found.",
                    Data = result
                });
            }
        }
        #endregion

        #region Get All Booked External Meetings
        /// <summary>
        /// Get All Booked External Meetings
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet("GetAllBookedExternalMeetings")]
        public async Task<IActionResult> GetAllBookedExternalMeetings()
        {
            var result = await Services_Repo.CalenderService.GetAllBookedExternalMeetingOrEvent();
            return Ok(result);
        }
        #endregion

        #region Get All External Meetings
        /// <summary>
        /// Get All External Meetings
        /// </summary>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpGet]
        [Route("GetAllExternalMeetings")]
        [ProducesResponseType(typeof(GenericResponse), 200)]
        [ProducesResponseType(typeof(GenericResponse), 400)]
        public async Task<IActionResult> GetAllExternalMeetings()
        {
            try
            {
                var result = await Services_Repo.CalenderService.GetAllExternalMeetingOrEvent();
                return Ok(result);
            }
            catch (RecordNotFoundException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
        }
        #endregion

        #region Lock External Meeting
        /// <summary>
        /// Lock External Meeting
        /// </summary>
        /// <param name="meetingId"></param>
        /// <param name="lockMeeting"></param>
        /// <returns></returns>
        [CustomAuthorize(Permissions.can_create_meetings)]
        [HttpPut]
        [Route("LockExternalMeeting/{meetingId}")]
        [ProducesResponseType(typeof(string), 200)]
        [ProducesResponseType(typeof(string), 400)]
        public async Task<IActionResult> LockExternalMeeting(string meetingId, bool lockMeeting)
        {
            try
            {
                var result = await Services_Repo.CalenderService.LockExternalMeetingOrEvent(meetingId, lockMeeting, CurrentUserId.ToString());
                if (result)
                {
                    // Log activity
                    var text = lockMeeting ? "locked" : "unlocked";
                    var description = $"{CurrentUser} {lockMeeting} External Meeting successfully";
                    var summary = $"External Meeting Locked/Unlocked";
                    await LogActivity(description, summary, meetingId);

                    return Ok(new ApiResponse<bool>
                    {
                        ResponseCode = "200",
                        ResponseMessage = "External Meeting locked/unlocked successfully.",
                        Data = result
                    });
                }
                else
                {
                    return BadRequest(new ApiResponse<bool>
                    {
                        ResponseCode = "400",
                        ResponseMessage = "External Meeting could not be locked.",
                        Data = result
                    });
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "401",
                    ResponseMessage = ex.Message,
                    Data = null
                });
            }
            catch (RecordNotFoundException ex)
            {
                _logger.Error(ex.Message, nameof(LockExternalMeeting));
                return BadRequest(new ApiResponse<ExternalMeeting>
                {
                    ResponseCode = "400",
                    DevResponseMessage = ex.Message,
                    ResponseMessage = "Something went wrong, try again later",
                    Data = null
                });
            }
        }
        #endregion

        #region Private Methods
        private async Task LogActivity(string description, string summary, string eventId = null, string userId = null)
        {
            var canLogActivity = await Services_Repo.ActivityService.CheckIfUserHasGrantedPermission(CurrentUserId?.ToString() ?? userId, EventCategory.Calender);
            if (canLogActivity)
            {
                var activity = new ActivityDto
                {
                    Description = description,
                    ActivitySummary = summary,
                    EventCategory = EventCategory.Calender,
                    UserId = CurrentUserId.ToString(),
                    By = CurrentUser,
                    EventId = eventId,
                    Application = Applications.Joble
                };

                await Services_Repo.ActivityService.CreateLog(activity);
            }
        }
        #endregion
    }
}
