using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Jobid.App.AdminConsole.Dto;
using Twilio.TwiML;
using Jobid.App.Helpers.Contract;

namespace Jobid.App.AdminConsole.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WebRTCController : ControllerBase
    {
        private readonly IUnitofwork _unitofwork;
        private readonly ILogger<WebRTCController> _logger;

        public WebRTCController(IUnitofwork unitofwork, ILogger<WebRTCController> logger)
        {
            _unitofwork = unitofwork;
            _logger = logger;
        }

        /// <summary>
        /// Register agent availability for WebRTC calls
        /// </summary>
        [HttpPost("register-agent")]
        [Authorize]
        public async Task<IActionResult> RegisterAgent([FromBody] WebRTCAgentRegistrationDto request)
        {
            var result = await _unitofwork.PhoneNumberService.RegisterAgentForWebRTCCalls(request.UserId, request.Available);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// Initiate WebRTC call to PSTN - this prepares the call setup
        /// Frontend will then use Twilio Voice SDK to actually make the call
        /// </summary>
        [HttpPost("make-call")]
        [Authorize]
        public async Task<IActionResult> MakeCall([FromBody] WebRTCCallRequestDto request)
        {
            // Read subdomain from header
            request.Subdomain = request.Subdomain ?? HttpContext.Request.Headers["subdomain"].ToString();
            var result = await _unitofwork.PhoneNumberService.InitiateWebRTCCall(request);

            if (result.ResponseCode == "200")
            {
                return Ok(result);
            }

            return BadRequest(result);
        }

        /// <summary>
        /// TwiML webhook for outbound WebRTC calls
        /// This is called when customer answers and needs to be connected to WebRTC browser
        /// </summary>

        [HttpPost("webhook/twiml/handle-outbound")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleOutboundTwiML(
            [FromForm] string From,
            [FromForm] string To,
            [FromForm] string CallSid,
            [FromQuery] string userId,
            [FromQuery] string callId)
        {
            try
            {
                _logger.LogInformation("Handling click-to-call TwiML for user {UserId}, CallSid {CallSid}", userId, CallSid);

                var twiml = await _unitofwork.PhoneNumberService.GenerateOutboundWebRTCTwiMLForUser(userId ?? "default-user", CallSid);
                return Content(twiml, "application/xml");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling outbound WebRTC TwiML");

                var errorResponse = new VoiceResponse();
                errorResponse.Say("Sorry, there was an error processing your call.");
                errorResponse.Hangup();

                return Content(errorResponse.ToString(), "application/xml");
            }
        }


        /// <summary>
        /// TwiML webhook for inbound calls to WebRTC browsers
        /// </summary>
        [HttpPost("twiml/inbound")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleInboundTwiML([FromForm] WebRTCTwiMLRequestDto request, [FromQuery] string subdomain)
        {
            try
            {
                var twiml = await _unitofwork.PhoneNumberService.GenerateInboundWebRTCTwiML(request.From, request.To, request.CallSid, subdomain);
                return Content(twiml, "application/xml");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling inbound WebRTC TwiML");

                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say>Sorry, there was an error processing your call.</Say>
    <Hangup/>
</Response>";

                return Content(errorTwiml, "application/xml");
            }
        }

        /// <summary>
        /// Handle WebRTC dial status callbacks for inbound calls
        /// Called when WebRTC agent answers/declines/doesn't answer
        /// </summary>
        [HttpPost("twiml/dial-status")]
        [AllowAnonymous]
        public async Task<IActionResult> HandleDialStatus([FromForm] string DialCallStatus, [FromForm] string CallSid, [FromQuery] string agent)
        {
            try
            {
                _logger.LogInformation("WebRTC dial status {Status} for call {CallSid} and agent {Agent}", DialCallStatus, CallSid, agent);

                var twiml = await _unitofwork.PhoneNumberService.HandleWebRTCDialStatus(DialCallStatus, CallSid, agent);
                return Content(twiml, "application/xml");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling WebRTC dial status for call {CallSid}", CallSid);

                var errorTwiml = @"<?xml version=""1.0"" encoding=""UTF-8""?>
<Response>
    <Say>There was an error processing your call.</Say>
    <Hangup/>
</Response>";

                return Content(errorTwiml, "application/xml");
            }
        }
    }
}
