﻿using Jobid.App.AdminConsole.Hubs;
using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Jobid.App.Notification.Hubs
{
    public sealed class NotificationHub : Hub<INotificationClient>
    {
        private readonly ILogger<NotificationHub> _logger;

        public NotificationHub(ILogger<NotificationHub> logger)
        {
            _logger = logger;
        }

        private string GetUserId()
        {
            return Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                   Context.User?.FindFirst("sub")?.Value ??
                   Context.User?.FindFirst("userId")?.Value ??
                   throw new UnauthorizedAccessException();
        }

        public override async Task OnConnectedAsync()
        {
            var userId = GetUserId();
            _logger.LogInformation("SignalR connection established for user: {UserId}, Connection ID: {ConnectionId}", userId, Context.ConnectionId);

            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception exception)
        {
            var userId = GetUserId();
            _logger.LogInformation("SignalR connection disconnected for user: {UserId}, Connection ID: {ConnectionId}", userId, Context.ConnectionId);

            // Clean up user connection mapping
            CustomUserIdProvider.RemoveUserConnection(Context.ConnectionId);

            await base.OnDisconnectedAsync(exception);
        }

        public async Task AssociateUserConnection(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogWarning("Cannot associate connection with empty user ID");
                return;
            }

            // Associate user with connection
            CustomUserIdProvider.AssociateUserConnection(userId, Context.ConnectionId);
            _logger.LogInformation("Associated user {UserId} with connection {ConnectionId}", userId, Context.ConnectionId);

            await Task.CompletedTask;
        }

        public async Task InvokeMethod()
            => await Clients.All.RecieveNotification();
    }

    public interface INotificationClient
    {
        Task RecieveNotification();
    }
}
