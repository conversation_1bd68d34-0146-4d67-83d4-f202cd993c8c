#region Using Statements
using Hangfire;
using HangfireBasicAuthenticationFilter;
using Jobid.App.ActivityLog.BackGroundJobs;
using Jobid.App.ActivityLog.contract;
using Jobid.App.AdminConsole.Contract;
using Jobid.App.Calender.Contracts;
using Jobid.App.Helpers.Attributes;
using Jobid.App.Helpers.Configurations;
using Jobid.App.Helpers.Context;
using Jobid.App.Helpers.Extensions;
using Jobid.App.Helpers.Middlewares;
using Jobid.App.Helpers.Services.Contract;
using Jobid.App.Helpers.Utils;
using Jobid.App.Helpers.ViewModel;
using Jobid.App.JobProject.Services.Contract;
using Jobid.App.JobProject.Services.Grpc.Server;
using Jobid.App.Notification.Hubs;
using Jobid.App.Subscription.Services.Grpc.Server;
using Jobid.App.Tenant.Repository;
using Jobid.App.Tenant.SchemaTenant;
using Jobid.App.Tenant.SchemaTenant.SchemaContext;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using PuppeteerSharp;
using Serilog;
using System;
using System.IO;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using WatchDog;
using static Jobid.App.Helpers.Attributes.ApiKeyAttribute;
using Jobid.App.AdminConsole.Services;
using Jobid.App.AdminConsole.Hubs;
using Microsoft.AspNetCore.SignalR;
using Npgsql;
#endregion

namespace Jobid
{
    public class Startup
    {
        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            Configuration = configuration;
            env = environment;
        }
        public IConfiguration Configuration { get; }
        public IWebHostEnvironment env { get; }

        public void ConfigureServices(IServiceCollection services)
        {
            // Read custom settings from customsettings.json
            string customSettingsPath = Path.Combine(Directory.GetCurrentDirectory(), "CustomAppSettings.json");
            string jsonString = File.ReadAllText(customSettingsPath);

            // Parse JSON string to object if needed
            var customAppSettings = JsonConvert.DeserializeObject<CustomAppSettings>(jsonString);
            var jwtKey = Configuration.GetValue<string>("JwtKey");

            var conn = string.Empty; var dataSource = Environment.GetEnvironmentVariable("JOBPRO_DATA_SOURCE");
            var database = Environment.GetEnvironmentVariable("JOBPRO_DATABASE");
            var username = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_USERNAME");
            var password = Environment.GetEnvironmentVariable("JOBPRO_DATABASE_PASSWORD");
            if (!string.IsNullOrEmpty(dataSource) && !string.IsNullOrEmpty(database) && !string.IsNullOrEmpty(username) && !string.IsNullOrEmpty(password))
            {
                conn = $"Host={dataSource};Database={database};Username={username};Password={password};Include Error Detail=true;";
            }
            else
            {
                // If environment from IWebHostEnvironment is development, use the Local Connection String
                if (env.IsDevelopment())
                {
                    conn = Configuration.GetConnectionString("LocalConnectionString");
                }
                else
                {
                    conn = Configuration.GetConnectionString("ConnectionString");
                }
            }

            // Log connection string details (without sensitive info)
            var connForLogging = conn.Contains("Password=")
                ? conn.Substring(0, conn.IndexOf("Password=")) + "Password=***"
                : conn;
            Log.Information("Configuring database connection: {ConnectionString}", connForLogging);

            // Optimize connection strings for different services using our new helper
            var optimizedMainConn = ConnectionStringOptimizer.OptimizeForMainApp(conn);
            var optimizedHangfireConn = ConnectionStringOptimizer.OptimizeForHangfire(conn);
            var optimizedBackgroundConn = ConnectionStringOptimizer.OptimizeForBackgroundServices(conn);

            // Assign global variables with optimized connection string
            GlobalVariables.ConnectionString = optimizedMainConn;
            GlobalVariables.CustomAppSettings = customAppSettings;
            GlobalVariables.JwtKey = jwtKey;
            GlobalVariables.Environment = Environment.GetEnvironmentVariable("JOBPRO_ENVIRONMENT");

            // Add Serilog Configuration
            ConfigureLogs.ConfigureSerilog();
            services.AddLogging(loggingBuilder =>
            {
                loggingBuilder.ClearProviders();
                loggingBuilder.AddSerilog(null, dispose: true);
            });

            var context = new CustomAssemblyLoadContext();
            var path = Path.Combine(Directory.GetCurrentDirectory(), "DinkToPdf", "libwkhtmltox.dll");
            //context.LoadUnmanagedLibrary(path); // Load the unmanaged library - Do not delete this, this is a better option for windows server [Dozie]

            // Add watchdog service with optimized connection settings and error handling
            try
            {
                var watchDogConnectionString = conn.WithWatchDogOptimizedSettings();
                if (!string.IsNullOrEmpty(watchDogConnectionString))
                {
                    services.AddWatchDogService(watchDogConnectionString);
                    Log.Information("WatchDog service registered successfully with optimized PostgreSQL settings");
                }
                else
                {
                    Log.Warning("WatchDog connection string is empty - WatchDog service not registered");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to register WatchDog service - application will continue without WatchDog functionality");
                // Don't throw - allow application to start without WatchDog
            }

            services.AddControllers().AddJsonOptions(o => o.JsonSerializerOptions.Converters.Add(new JsonStringEnumConverter())).AddNewtonsoftJson(o =>
            {
                o.SerializerSettings.ReferenceLoopHandling = ReferenceLoopHandling.Ignore;
                o.SerializerSettings.ContractResolver = new DefaultContractResolver();
            });

            // order is vital, this *must* be called *after* AddNewtonsoftJson()
            services.AddSwaggerGenNewtonsoftSupport();

            // 1) Schema Modification: Make Context Schema Aware
            if (Environment.GetEnvironmentVariable("IsTesting") == "true")
            {
                services.AddDbContext<JobProDbContext>(builder => builder.UseInMemoryDatabase("InMemoryDb").ReplaceService<IMigrationsAssembly, DbSchemaAwareMigrationAssembly>().ReplaceService<IModelCacheKeyFactory, DbSchemaAwareModelCacheKeyFactory>()).AddSingleton<IDbContextSchema>(new DbContextSchema());
            }
            else
            {
                services.AddDbContext<JobProDbContext>(builder => builder.UseNpgsql(conn.WithOptimizedSettings())
                    .ReplaceService<IMigrationsAssembly, DbSchemaAwareMigrationAssembly>()
                    .ReplaceService<IModelCacheKeyFactory, DbSchemaAwareModelCacheKeyFactory>()
                    ).AddSingleton<IDbContextSchema>(new DbContextSchema());
            }

            // Add stripe configuration values
            // services.AddStripeService(Configuration);

            services.Configure<RtcSettings>(Configuration.GetSection("RtcNotification"));
            services.AddScoped<ApiKeyAttribute>();

            // Add Health Check
            services.AddHealthChecks();

            // Configure SignalR endpoints
            services.AddSignalR(options =>
            {
                options.EnableDetailedErrors = true;
            }).AddHubOptions<CallHub>(options =>
            {
                options.EnableDetailedErrors = true;
            });

            // Register custom user ID provider for SignalR
            services.AddSingleton<IUserIdProvider, CustomUserIdProvider>();

            // Add Memory Cache for WebRTC agent management
            services.AddMemoryCache();

            // Add hangfire Configuration with optimized connection string
            services.AddHangfireConfigurations(optimizedHangfireConn);
            services.AddCustomServices(Configuration);

            services.AddHttpClient(); // Required for HTTP API calls

            // Add the database connection manager as a singleton
            services.AddSingleton<DbConnectionManager>();

            // Add the database job executor for Hangfire jobs
            services.AddScoped<DatabaseJobExecutor>();

            // Get IRedisCacheService as a required service
            var scopeFactory = services.BuildServiceProvider().GetRequiredService<IServiceScopeFactory>();
            using (var scope = scopeFactory.CreateScope())
            {
                var serviceProvider = scope.ServiceProvider;
                GlobalVariables.RedisCacheService = serviceProvider.GetRequiredService<IRedisCacheService>();
            }

            // Download Chromium for PuppeteerSharp
            //PuppeteerInitializer.InitializeAsync().GetAwaiter().GetResult();
        }

        public void Configure(IApplicationBuilder app,
            IWebHostEnvironment env,
            IServiceProvider serviceProvider,
            TenantService tenantService,
            IConfiguration config,
            IBackGroundServices backGroundServices,
            IProductUpdateReceiverService productUpdateReceiverService,
            IActivityViewBackgroundService activityViewBackgroundService,
            IBackgroungService adminConsoleBackgroungService,
            IActivityBackgroundService activityBackgroundService,
            IBackGroundService calenderBackGroundService,
            IPhoneNumberMaintenanceService phoneNumberMaintenanceService)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            // Trigger Background Jobs On Start Up
            if (env.IsDevelopment())
            {
                // Log that this block of code was entered
                Log.Information("Triggering Background Jobs On Start Up");
                WatchLogger.Log("Triggering Background Jobs On Start Up");

                var hangFireScheduller = new HangFireJobScheduler(backGroundServices, productUpdateReceiverService, activityViewBackgroundService, adminConsoleBackgroungService, activityBackgroundService, calenderBackGroundService, phoneNumberMaintenanceService);

                // Schedule recurring jobs with retry logic for serialization errors
                StartupRetryMiddleware.ExecuteStartupOperation(
                    serviceProvider,
                    () => hangFireScheduller.ScheduleRecurringJobs(),
                    "Schedule Hangfire Recurring Jobs",
                    maxRetries: 3,
                    baseDelayMs: 1000,
                    isRequired: false // Not required for startup to continue
                );

                // Schedule one-time jobs with retry logic for serialization errors
                StartupRetryMiddleware.ExecuteStartupOperation(
                    serviceProvider,
                    () => hangFireScheduller.ScheduleOneTimeJobs(),
                    "Schedule Hangfire One-Time Jobs",
                    maxRetries: 3,
                    baseDelayMs: 1000,
                    isRequired: false // Not required for startup to continue
                );
            }

            app.CustomEnginerInterceptor();
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "JobPro v1");
                c.RoutePrefix = "swagger";
            });

            // app.UseHttpsRedirection();
            app.UseForwardedHeaders(new ForwardedHeadersOptions
            {
                ForwardedHeaders = ForwardedHeaders.XForwardedFor | ForwardedHeaders.XForwardedProto
            });

            // Hangfire Dashboard
            app.UseHangfireDashboard("/hangfire", new DashboardOptions
            {
                DashboardTitle = "Hangfire Dashboard",
                Authorization = new[]
                {
                    new HangfireCustomBasicAuthenticationFilter()
                    {
                        Pass = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_PASSWORD") ?? config["HangfireCredentials:Password"],
                        User = Environment.GetEnvironmentVariable("JOBPRO_HANGFIRE_USERNAME") ?? config["HangfireCredentials:Username"]
                    }
                },
                IgnoreAntiforgeryToken = true
            });

            app.UseWebSockets();
            app.UseStaticFiles();
            app.UseRouting();
            app.UseCors("MyPolicy");
            app.UseAuthentication();
            app.UseAuthorization();

            // Add WatchDog Exception Logger early in the pipeline
            app.UseWatchDogExceptionLogger();

            // Add custom middlewares
            app.UseMiddleware<ExceptionMiddleware>();
            app.UseMiddleware<TokenValidationMiddleware>();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
                endpoints.MapHub<NotificationHub>("/notificationHub");
                endpoints.MapHub<SmartLoginHub>("/smart-login");
                endpoints.MapGrpcService<SubscriptionGrpcService>();
                endpoints.MapGrpcService<ProjectGrpcService>();
                endpoints.MapHub<CallHub>("/callHub");
            });

            // Add WatchDog Dashboard after routing with error handling
            try
            {
                var watchDogUsername = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_USERNAME") ?? Configuration["WatchDogCredentials:Username"];
                var watchDogPassword = Environment.GetEnvironmentVariable("JOBPRO_WATCHDOG_PASSWORD") ?? Configuration["WatchDogCredentials:Password"];

                if (string.IsNullOrEmpty(watchDogUsername) || string.IsNullOrEmpty(watchDogPassword))
                {
                    Log.Warning("WatchDog credentials not found. Dashboard will not be available.");
                }
                else
                {
                    app.UseWatchDog(opt =>
                    {
                        opt.WatchPageUsername = watchDogUsername;
                        opt.WatchPagePassword = watchDogPassword;
                    });
                    Log.Information("WatchDog dashboard configured successfully");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to configure WatchDog dashboard. Continuing without dashboard.");
            }

            // Initialize WatchDog with enhanced retry logic and fallback mechanisms
            StartupRetryMiddleware.ExecuteStartupOperation(
                serviceProvider,
                () =>
                {
                    try
                    {
                        // Test WatchDog connectivity first
                        WatchLogger.Log("WatchDog service initialization test");

                        // If successful, log the actual initialization message
                        WatchLogger.Log("WatchDog service initialized successfully");
                        Log.Information("WatchDog logging service is operational");
                    }
                    catch (PostgresException pgEx) when (pgEx.SqlState == "40001")
                    {
                        // Let the retry middleware handle PostgreSQL serialization errors
                        Log.Warning("PostgreSQL serialization error during WatchDog initialization - will retry");
                        throw;
                    }
                    catch (Exception ex)
                    {
                        // Log to Serilog as fallback if WatchDog fails
                        Log.Error(ex, "Failed to initialize WatchDog logging - falling back to Serilog only");

                        // Don't throw for non-critical WatchDog failures
                        // The application can continue without WatchDog
                        Log.Warning("Application will continue without WatchDog logging functionality");
                    }
                },
                "Initialize WatchDog Service",
                maxRetries: 5,
                baseDelayMs: 2000,
                isRequired: false // Not required for startup to continue
            );

            // Delete all keys from redis cache
            Utility.DeleteAllRedisKeys(serviceProvider);

            // Run migration file on startup
            if (!env.IsDevelopment())
            {
                StartupRetryMiddleware.ExecuteStartupOperation(
                    serviceProvider,
                    async () => await Task.Run(() => app.ApplyMigration()),
                    "Apply Database Migrations",
                    maxRetries: 5,
                    baseDelayMs: 3000,
                    isRequired: true // Required for production startup
                ).GetAwaiter().GetResult();
            }

            // Seed data
            if (!env.IsDevelopment())
            {
                StartupRetryMiddleware.ExecuteStartupOperation(
                    serviceProvider,
                    async () => await Task.Run(() => app.UseSeedData()),
                    "Seed Database Data",
                    maxRetries: 3,
                    baseDelayMs: 2000,
                    isRequired: false // Not required if it fails
                ).GetAwaiter().GetResult();
            }
        }
    }
}
