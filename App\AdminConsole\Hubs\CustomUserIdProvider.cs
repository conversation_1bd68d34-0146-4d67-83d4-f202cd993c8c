using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;
using System.Collections.Concurrent;
using System.Linq;
using System;

namespace Jobid.App.AdminConsole.Hubs
{
    public class CustomUserIdProvider : IUserIdProvider
    {
        // Static dictionary to track user ID to connection ID mappings
        private static readonly ConcurrentDictionary<string, string> _userConnections = new();
        
        public static void AssociateUserConnection(string userId, string connectionId)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(connectionId))
                return;
                
            // Remove any existing mapping for this user
            var existingConnection = _userConnections.FirstOrDefault(kvp => kvp.Key == userId);

            // Remove existing connection for user if it exists
            _userConnections.AddOrUpdate(userId, connectionId, (key, oldValue) => connectionId);
        }

        public static void RemoveUserConnection(string connectionId)
        {
            if (string.IsNullOrEmpty(connectionId))
                return;

            var match = _userConnections.FirstOrDefault(kvp => kvp.Value == connectionId);

            if (!string.IsNullOrEmpty(match.Key))
            {
                _userConnections.TryRemove(match.Key, out _);
            }
            else
            {
                // optional: log or ignore
                Console.WriteLine($"Connection ID {connectionId} not found in user connection map.");
            }
        }

        public static string GetConnectionId(string userId)
        {
            _userConnections.TryGetValue(userId, out var connectionId);
            return connectionId;
        }

        public string GetUserId(HubConnectionContext connection)
        {
            // First try to get from JWT claims
            var userId = connection.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                        connection.User?.FindFirst("sub")?.Value ??
                        connection.User?.FindFirst("userId")?.Value;
            
            if (!string.IsNullOrEmpty(userId))
            {
                return userId;
            }
            
            // If no claims, check if this connection has been manually associated
            var associatedUser = _userConnections.FirstOrDefault(kvp => kvp.Value == connection.ConnectionId);
            if (!associatedUser.Equals(default))
            {
                return associatedUser.Key;
            }
            
            // Return null if no user can be determined
            return null;
        }
    }
}
