using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using WatchDog;
using Jobid.App.Helpers.Utils;
using Serilog;

namespace Jobid.App.Helpers.Services.Implementations
{
    /// <summary>
    /// Wrapper service for WatchDog operations with retry logic for PostgreSQL serialization errors
    /// </summary>
    public static class WatchDogServiceWrapper
    {
        /// <summary>
        /// Logs a message with retry logic for PostgreSQL serialization errors
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="eventId">Optional event ID</param>
        /// <param name="source">Optional source information</param>
        /// <param name="extraData">Optional extra data</param>
        /// <returns>Task indicating completion</returns>
        public static async Task LogWithRetryAsync(string message, string eventId = "", string source = "", string extraData = "")
        {
            try
            {
                await RetryHelper.RetryOnSerializationError(async () =>
                {
                    await Task.Run(() => WatchLogger.Log(message, eventId, source, extraData)).ConfigureAwait(false);
                }, maxRetries: 3, delayMs: 1000).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // Fallback to Serilog if WatchDog fails completely
                Log.Error(ex, "Failed to log message to WatchDog after retries: {Message}", message);
            }
        }

        /// <summary>
        /// Logs an error with retry logic for PostgreSQL serialization errors
        /// </summary>
        /// <param name="message">The error message to log</param>
        /// <param name="eventId">Optional event ID</param>
        /// <param name="source">Optional source information</param>
        /// <param name="extraData">Optional extra data</param>
        /// <returns>Task indicating completion</returns>
        public static async Task LogErrorWithRetryAsync(string message, string eventId = "", string source = "", string extraData = "")
        {
            try
            {
                await RetryHelper.RetryOnSerializationError(async () =>
                {
                    await Task.Run(() => WatchLogger.LogError(message, eventId, source, extraData)).ConfigureAwait(false);
                }, maxRetries: 3, delayMs: 1000).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // Fallback to Serilog if WatchDog fails completely
                Log.Error(ex, "Failed to log error to WatchDog after retries: {Message}", message);
            }
        }

        /// <summary>
        /// Logs a message synchronously with retry logic for PostgreSQL serialization errors
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="eventId">Optional event ID</param>
        /// <param name="source">Optional source information</param>
        /// <param name="extraData">Optional extra data</param>
        public static void LogWithRetry(string message, string eventId = "", string source = "", string extraData = "")
        {
            try
            {
                // Use synchronous retry helper to avoid deadlock issues
                RetryHelper.RetryOnSerializationError(() =>
                {
                    WatchLogger.Log(message, eventId, source, extraData);
                }, maxRetries: 3, delayMs: 1000);
            }
            catch (Exception ex)
            {
                // Fallback to Serilog if WatchDog fails completely
                Log.Error(ex, "Failed to log message to WatchDog after retries: {Message}", message);
            }
        }

        /// <summary>
        /// Logs an error synchronously with retry logic for PostgreSQL serialization errors
        /// </summary>
        /// <param name="message">The error message to log</param>
        /// <param name="eventId">Optional event ID</param>
        /// <param name="source">Optional source information</param>
        /// <param name="extraData">Optional extra data</param>
        public static void LogErrorWithRetry(string message, string eventId = "", string source = "", string extraData = "")
        {
            try
            {
                // Use synchronous retry helper to avoid deadlock issues
                RetryHelper.RetryOnSerializationError(() =>
                {
                    WatchLogger.LogError(message, eventId, source, extraData);
                }, maxRetries: 3, delayMs: 1000);
            }
            catch (Exception ex)
            {
                // Fallback to Serilog if WatchDog fails completely
                Log.Error(ex, "Failed to log error to WatchDog after retries: {Message}", message);
            }
        }
    }
}
